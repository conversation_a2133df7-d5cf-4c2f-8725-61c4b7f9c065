/**
 * 清空数据库中的所有图书数据
 * 让用户重新导入真实的图书
 */

const fs = require('fs')
const path = require('path')

console.log('🗑️ 清空数据库中的图书数据...\n')

// 数据库文件路径
const dbPath = path.join(__dirname, 'database', 'yu-reader.db')

console.log('数据库路径:', dbPath)

if (!fs.existsSync(dbPath)) {
  console.log('❌ 数据库文件不存在:', dbPath)
  process.exit(1)
}

// 创建一个简单的SQL脚本来清空books表
const clearSQL = `
-- 清空books表中的所有数据
DELETE FROM books;

-- 重置自增ID
DELETE FROM sqlite_sequence WHERE name='books';

-- 验证清空结果
SELECT COUNT(*) as remaining_books FROM books;
`

// 将SQL写入临时文件
const tempSQLPath = path.join(__dirname, 'temp-clear.sql')
fs.writeFileSync(tempSQLPath, clearSQL)

console.log('📝 已创建清理SQL脚本:', tempSQLPath)
console.log('\n🔧 请手动执行以下步骤来清理数据库:')
console.log('1. 停止Electron应用')
console.log('2. 使用SQLite工具执行清理脚本')
console.log('3. 或者直接删除数据库文件让应用重新创建')
console.log('\n💡 推荐方案：直接删除数据库文件')

// 提供删除数据库文件的选项
const readline = require('readline')
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
})

rl.question('是否要删除数据库文件？(y/N): ', (answer) => {
  if (answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes') {
    try {
      fs.unlinkSync(dbPath)
      console.log('✅ 数据库文件已删除:', dbPath)
      console.log('🔄 请重启应用，系统将创建新的空数据库')
    } catch (error) {
      console.error('❌ 删除数据库文件失败:', error.message)
      console.log('💡 请手动删除文件:', dbPath)
    }
  } else {
    console.log('⏭️ 跳过删除数据库文件')
    console.log('💡 您可以手动删除文件:', dbPath)
  }
  
  // 清理临时文件
  try {
    fs.unlinkSync(tempSQLPath)
  } catch (error) {
    // 忽略清理临时文件的错误
  }
  
  rl.close()
})
