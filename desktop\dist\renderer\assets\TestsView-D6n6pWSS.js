import{d as o,f as a,c as n,g as e,a as c,r as p,o as _,_ as d}from"./index-BqsStGhU.js";const r={class:"tests-view"},i={class:"tests-content"},l=o({__name:"TestsView",setup(m){return a(()=>{console.log("测试任务页面已加载")}),(f,s)=>{const t=p("el-empty");return _(),n("div",r,[s[0]||(s[0]=e("div",{class:"page-header"},[e("h1",{class:"page-title"},"测试任务"),e("p",{class:"page-description"},"参与在线测试和评估")],-1)),e("div",i,[c(t,{description:"测试任务功能开发中..."})])])}}}),g=d(l,[["__scopeId","data-v-ed6331ce"]]);export{g as default};
