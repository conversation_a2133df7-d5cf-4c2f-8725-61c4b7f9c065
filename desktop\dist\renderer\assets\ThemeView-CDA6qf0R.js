import{d as t,f as a,c as n,g as e,a as c,r as p,o as d,_}from"./index-BqsStGhU.js";const r={class:"theme-view"},i={class:"theme-content"},l=t({__name:"ThemeView",setup(m){return a(()=>{console.log("主题设置页面已加载")}),(h,s)=>{const o=p("el-empty");return d(),n("div",r,[s[0]||(s[0]=e("div",{class:"page-header"},[e("h1",{class:"page-title"},"主题设置"),e("p",{class:"page-description"},"外观和主题配置")],-1)),e("div",i,[c(o,{description:"主题设置功能开发中..."})])])}}}),v=_(l,[["__scopeId","data-v-d2eced58"]]);export{v as default};
