import{d as b,b as l,f as w,c as u,g as t,a as n,i as c,u as _,ad as y,r as C,t as e,$ as D,ae as B,F as V,n as $,q as x,af as S,H as T,o as d,_ as q}from"./index-BqsStGhU.js";const E={class:"bookmark-view"},F={class:"bookmark-content"},L={class:"quick-nav"},M={class:"nav-icon"},N={class:"nav-content"},R={class:"nav-count"},j={class:"nav-icon"},A={class:"nav-content"},H={class:"nav-count"},I={class:"nav-icon"},J={class:"nav-content"},P={class:"nav-count"},z={class:"recent-section"},G={class:"activity-list"},K={class:"activity-icon"},O={class:"activity-content"},Q={class:"activity-title"},U={class:"activity-book"},W={class:"activity-time"},X=b({__name:"BookmarkView",setup(Y){const k=T(),p=l(15),m=l(8),f=l(23),h=l([{id:"1",title:"添加了新书签",book:"JavaScript高级程序设计",time:Date.now()-36e5,icon:"Star"},{id:"2",title:"创建了新笔记",book:"Vue.js设计与实现",time:Date.now()-72e5,icon:"EditPen"},{id:"3",title:"高亮了重要段落",book:"React技术揭秘",time:Date.now()-108e5,icon:"Brush"}]),r=a=>{k.push(a)},g=a=>{const s=new Date(a),o=new Date().getTime()-s.getTime(),v=Math.floor(o/(1e3*60*60));return v<1?"刚刚":v<24?`${v}小时前`:s.toLocaleDateString()};return w(()=>{console.log("书签笔记页面已加载")}),(a,s)=>{const i=C("el-icon");return d(),u("div",E,[s[10]||(s[10]=t("div",{class:"page-header"},[t("h1",{class:"page-title"},"书签笔记"),t("p",{class:"page-description"},"管理您的阅读标记和笔记")],-1)),t("div",F,[t("div",L,[t("div",{class:"nav-card",onClick:s[0]||(s[0]=o=>r("/bookmark/bookmarks"))},[t("div",M,[n(i,null,{default:c(()=>[n(_(y))]),_:1})]),t("div",N,[s[3]||(s[3]=t("h3",null,"书签管理",-1)),s[4]||(s[4]=t("p",null,"查看所有书签",-1)),t("span",R,e(p.value)+" 个书签",1)])]),t("div",{class:"nav-card",onClick:s[1]||(s[1]=o=>r("/bookmark/notes"))},[t("div",j,[n(i,null,{default:c(()=>[n(_(D))]),_:1})]),t("div",A,[s[5]||(s[5]=t("h3",null,"笔记管理",-1)),s[6]||(s[6]=t("p",null,"整理阅读笔记",-1)),t("span",H,e(m.value)+" 条笔记",1)])]),t("div",{class:"nav-card",onClick:s[2]||(s[2]=o=>r("/bookmark/highlights"))},[t("div",I,[n(i,null,{default:c(()=>[n(_(B))]),_:1})]),t("div",J,[s[7]||(s[7]=t("h3",null,"高亮管理",-1)),s[8]||(s[8]=t("p",null,"管理文本高亮",-1)),t("span",P,e(f.value)+" 个高亮",1)])])]),t("div",z,[s[9]||(s[9]=t("h2",null,"最近活动",-1)),t("div",G,[(d(!0),u(V,null,$(h.value,o=>(d(),u("div",{key:o.id,class:"activity-item"},[t("div",K,[n(i,null,{default:c(()=>[(d(),x(S(o.icon)))]),_:2},1024)]),t("div",O,[t("div",Q,e(o.title),1),t("div",U,e(o.book),1),t("div",W,e(g(o.time)),1)])]))),128))])])])])}}}),tt=q(X,[["__scopeId","data-v-ae7ca913"]]);export{tt as default};
