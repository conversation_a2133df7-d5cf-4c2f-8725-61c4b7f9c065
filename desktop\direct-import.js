/**
 * 直接通过 Electron 主进程执行批量导入
 * 绕过浏览器工具，直接调用 BookService
 */

const fs = require('fs')
const path = require('path')

// 导入 Electron 相关模块
const { app, BrowserWindow } = require('electron')

console.log('🚀 直接执行批量导入...\n')

// 读取图书数据
const scriptPath = path.join(__dirname, 'import-script.js')
const scriptContent = fs.readFileSync(scriptPath, 'utf8')
const booksMatch = scriptContent.match(/const booksToImport = (\[[\s\S]*?\]);/)
const booksToImport = JSON.parse(booksMatch[1])

console.log(`📊 准备导入 ${booksToImport.length} 本图书`)

// 创建一个简单的导入执行器
async function executeDirectImport() {
  try {
    // 等待应用准备就绪
    if (!app.isReady()) {
      console.log('⏳ 等待 Electron 应用准备就绪...')
      await app.whenReady()
    }

    // 获取主窗口
    const windows = BrowserWindow.getAllWindows()
    if (windows.length === 0) {
      console.error('❌ 没有找到 Electron 窗口')
      return
    }

    const mainWindow = windows[0]
    console.log('✅ 找到主窗口，开始执行导入脚本')

    // 构建要在渲染进程中执行的脚本
    const executeScript = `
      (async function() {
        console.log('🎯 开始批量导入 ${booksToImport.length} 本图书...');
        
        const booksToImport = ${JSON.stringify(booksToImport, null, 2)};
        
        let successCount = 0;
        let failCount = 0;
        const results = [];
        
        // 检查导入前状态
        const beforeBooks = await window.electronAPI.book.list();
        console.log(\`导入前数据库中有 \${beforeBooks.length} 本图书\`);
        
        for (let i = 0; i < booksToImport.length; i++) {
          const book = booksToImport[i];
          console.log(\`\\n[\${i + 1}/\${booksToImport.length}] 正在导入: \${book.name}\`);
          console.log(\`  路径: \${book.path}\`);
          console.log(\`  格式: \${book.format.toUpperCase()}\`);
          console.log(\`  大小: \${(book.size / 1024).toFixed(1)} KB\`);
          
          try {
            // 调用导入API
            const result = await window.electronAPI.book.add(book.path);
            
            console.log(\`  ✅ 导入成功: \${result.title || book.name}\`);
            console.log(\`     ID: \${result.id}\`);
            console.log(\`     作者: \${result.author || '未知'}\`);
            console.log(\`     文件格式: \${result.file_format}\`);
            console.log(\`     文件大小: \${result.file_size} 字节\`);
            
            results.push({
              file: book.name,
              success: true,
              result: result,
              message: '导入成功'
            });
            
            successCount++;
            
            // 短暂延迟避免过快导入
            await new Promise(resolve => setTimeout(resolve, 100));
            
          } catch (error) {
            console.error(\`  ❌ 导入失败: \${error.message}\`);
            
            results.push({
              file: book.name,
              success: false,
              error: error.message,
              message: \`导入失败: \${error.message}\`
            });
            
            failCount++;
          }
        }
        
        // 检查导入后状态
        const afterBooks = await window.electronAPI.book.list();
        console.log(\`\\n📊 导入完成统计:\`);
        console.log(\`  总计: \${booksToImport.length} 本\`);
        console.log(\`  成功: \${successCount} 本\`);
        console.log(\`  失败: \${failCount} 本\`);
        console.log(\`  导入前: \${beforeBooks.length} 本\`);
        console.log(\`  导入后: \${afterBooks.length} 本\`);
        console.log(\`  实际新增: \${afterBooks.length - beforeBooks.length} 本\`);
        
        // 显示新导入的图书信息
        if (afterBooks.length > beforeBooks.length) {
          console.log(\`\\n📚 新导入的图书:\`);
          const newBooks = afterBooks.slice(beforeBooks.length);
          newBooks.forEach((book, index) => {
            console.log(\`  \${index + 1}. 《\${book.title}》 - \${book.author || '未知作者'}\`);
            console.log(\`     格式: \${book.file_format} | 大小: \${book.file_size} 字节\`);
            console.log(\`     导入时间: \${book.import_time}\`);
          });
        }
        
        console.log(\`\\n🎉 批量导入完成!\`);
        
        return {
          total: booksToImport.length,
          success: successCount,
          failed: failCount,
          beforeCount: beforeBooks.length,
          afterCount: afterBooks.length
        };
      })();
    `

    // 在渲染进程中执行脚本
    const result = await mainWindow.webContents.executeJavaScript(executeScript)
    
    console.log('\n✅ 批量导入执行完成!')
    console.log('📊 最终结果:', result)
    
    return result

  } catch (error) {
    console.error('💥 执行导入失败:', error)
    throw error
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  executeDirectImport().then(result => {
    console.log('\n🎉 导入任务完成!', result)
    process.exit(0)
  }).catch(error => {
    console.error('\n💥 导入任务失败:', error)
    process.exit(1)
  })
}

module.exports = { executeDirectImport }
