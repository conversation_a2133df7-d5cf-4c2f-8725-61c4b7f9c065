import{d as Ge,s as Qe,b as D,e as qe,w as ce,f as He,c as u,g as s,h as m,t as r,u as n,a as t,i as a,j as d,p as Je,r as c,k as Ke,l as Oe,m as X,F as P,n as U,q as z,v as We,x as Xe,y as Ye,z as Ze,A as et,B as tt,C as k,D as _e,E as ge,G as pe,H as lt,I as v,J as at,o as i,_ as ot}from"./index-BqsStGhU.js";import{u as nt}from"./unifiedLibrary-BeO2bRfS.js";const st={class:"unified-library-view"},it={class:"library-header"},rt={class:"header-left"},dt={class:"stats-info"},ut={key:0},ct={key:1},_t={class:"header-actions"},gt={class:"toolbar"},pt={class:"toolbar-left"},ft={class:"toolbar-right"},mt={class:"result-count"},vt={key:0,class:"initialization-container"},ht={class:"initialization-content"},yt={class:"initialization-progress"},kt={key:1,class:"retry-container"},wt={class:"retry-content"},bt={class:"retry-progress"},Ct={key:2,class:"loading-container"},zt={key:3,class:"error-container"},xt={class:"debug-info",style:{background:"#ffe6e6",padding:"10px",margin:"10px 0","font-size":"12px",border:"1px solid #ff9999"}},Bt={class:"error-actions"},St={key:0,class:"retry-info"},Vt={key:4,class:"empty-container"},Pt={key:5,class:"books-grid"},Ut=["onClick"],Et={class:"book-cover"},Mt=["src","alt"],$t={key:1,class:"default-cover"},Rt={class:"format-badge"},At={key:2,class:"progress-overlay"},Ft={class:"book-info"},It=["title"],Lt=["title"],Tt={class:"book-meta"},Dt={class:"file-size"},Nt={key:0,class:"book-tags"},jt={key:0,class:"more-tags"},Gt={class:"book-actions"},Qt={key:6,class:"books-list"},qt={class:"list-cover"},Ht=["src","alt"],Jt={key:1,class:"default-list-cover"},Kt={class:"book-title-cell"},Ot={key:0,class:"book-description"},Wt={key:7,class:"pagination-container"},B=3,Xt=Ge({__name:"UnifiedLibraryView",setup(Yt){const S=lt(),Y=nt(),{books:x,loading:E,error:M,searchQuery:N,filter:j,sortField:fe,sortOrder:Zt,viewMode:G,pageSize:Q,currentPage:q,paginatedBooks:w,filteredBooks:H,stats:V,availableFormats:me,availableAuthors: <AUTHORS>
