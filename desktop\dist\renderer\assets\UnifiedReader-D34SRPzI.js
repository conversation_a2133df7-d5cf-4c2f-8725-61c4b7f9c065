var It=Object.defineProperty;var Rt=(w,t,n)=>t in w?It(w,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):w[t]=n;var me=(w,t,n)=>Rt(w,typeof t!="symbol"?t+"":t,n);import{N as Tt,b as f,R as At,e as oe,d as Ve,S as Et,f as Fe,T as ke,U as Ge,w as We,c as k,g as e,h as K,a as l,i as C,j as J,u as _,V as Be,r as Z,t as U,W as Ye,X as Ze,Y as Qe,Z as qe,$ as Je,l as Pe,a0 as St,a1 as tt,E as ye,v as ge,a2 as ot,a3 as Ut,x as Bt,F as Ce,n as $e,A as ze,a4 as nt,a5 as zt,a6 as lt,a7 as Le,H as je,I as be,o as m,_ as Me,q as Ue,a8 as Lt,z as Vt}from"./index-BqsStGhU.js";import{u as Ft}from"./unifiedLibrary-BeO2bRfS.js";function Mt(w){const{font:t,page:n}=w;let a;if(typeof document<"u"){const N=document.createElement("canvas").getContext("2d");N.font=`${t.weight} ${t.size}px ${t.family}`,a=N.measureText("中").width}else a=Dt(t.size,t.family);const d=t.size*t.lineHeight,r=n.width-n.marginLeft-n.marginRight,u=n.height-n.marginTop-n.marginBottom,i=Math.floor(r/a),c=Math.floor(u/d),b=i*c;return{charWidth:a,lineHeight:d,charsPerLine:i,linesPerPage:c,charsPerPage:b}}function Dt(w,t){let n=1;return t.includes("monospace")?n=.6:t.includes("serif")?n=.9:n=.85,w*n}function Xe(w,t){const n=Mt(t),a=[],d=w.replace(/\r\n/g,`
`).replace(/\r/g,`
`);let r=0,u=1;for(;r<d.length;){const c=Ht(d,r,u,n);a.push(c),r=c.endPosition,u++}const i=a.length>0?Math.round(d.length/a.length):0;return{totalPages:a.length,pages:a,averageCharsPerPage:i}}function Ht(w,t,n,a){const{charsPerLine:d,linesPerPage:r}=a;let u=t,i=0,c="";for(;i<r&&u<w.length;){const b=Nt(w,u,d);if(b.content.length===0)break;c+=b.content,u=b.nextPosition,i++,b.isNewParagraph&&(i+=.5)}return{pageNumber:n,startPosition:t,endPosition:u,content:c,lineCount:i}}function Nt(w,t,n){if(t>=w.length)return{content:"",nextPosition:t,isNewParagraph:!1};const a=w.indexOf(`
`,t),d=a!==-1;let r;if(d&&a-t<=n)r=a+1;else if(r=Math.min(t+n,w.length),r<w.length){const c=Ot(w,t,r);c>t&&(r=c)}const u=w.substring(t,r),i=d&&a<r;return{content:u,nextPosition:r,isNewParagraph:i}}function Ot(w,t,n){for(let a=n-1;a>t;a--){const d=w[a];if(d===" "||d==="	"||d==="-"||d==="，"||d==="。")return a+1}return n}function Oe(w,t){for(const n of t)if(w>=n.startPosition&&w<n.endPosition)return n.pageNumber;return t.length>0?t[t.length-1].pageNumber:1}function et(w,t){return t.find(n=>n.pageNumber===w)||null}function Gt(w,t){return t===0?0:Math.round(w/t*100)}class Wt{constructor(t){me(this,"bookContent",null);me(this,"originalText","");me(this,"paginationResult",null);me(this,"currentSettings");me(this,"events",{});me(this,"encodingResult",null);this.currentSettings={font:{family:"Microsoft YaHei, SimSun, serif",size:16,weight:"normal",lineHeight:1.6},page:{width:800,height:600,marginTop:40,marginBottom:40,marginLeft:60,marginRight:60},theme:{mode:"light",backgroundColor:"#ffffff",textColor:"#333333"},readingMode:"pagination",zoomLevel:1,...t}}async loadBook(t){var n,a,d,r,u,i;try{const c=await this.readFileContent(t);this.originalText=c.content,this.encodingResult={encoding:c.encoding,confidence:1,text:c.content},console.log(`TxtReader.loadBook: 文件内容长度 ${((n=this.originalText)==null?void 0:n.length)||0}`),console.log(`TxtReader.loadBook: 编码 ${this.encodingResult.encoding}`),console.log(`TxtReader.loadBook: 内容预览: ${((a=this.originalText)==null?void 0:a.substring(0,200))||"空内容"}`);const b=this.detectChapters(this.originalText);return this.paginationResult=Xe(this.originalText,this.currentSettings),console.log(`TxtReader.loadBook: 分页完成，总页数 ${this.paginationResult.totalPages}`),this.bookContent={id:this.generateBookId(t),title:this.extractTitle(t),author:"未知作者",filePath:t,format:"txt",totalPages:this.paginationResult.totalPages,currentPage:1,progress:0,content:this.originalText,chapters:b,metadata:{fileSize:c.size,createdAt:new Date,language:this.detectLanguage(this.originalText)}},(r=(d=this.events).onLoadComplete)==null||r.call(d,this.bookContent),this.bookContent}catch(c){const b=c instanceof Error?c:new Error(String(c));throw(i=(u=this.events).onError)==null||i.call(u,b),b}}async getPageContent(t){var d,r,u,i,c;if(console.log(`TxtReader.getPageContent: 请求第 ${t} 页`),!this.paginationResult||!this.bookContent)throw console.error("TxtReader.getPageContent: 书籍未加载"),new Error("书籍未加载");console.log(`TxtReader.getPageContent: 总页数 ${this.paginationResult.totalPages}`);const n=et(t,this.paginationResult.pages);if(!n)throw console.error(`TxtReader.getPageContent: 页面 ${t} 不存在`),new Error(`页面 ${t} 不存在`);console.log("TxtReader.getPageContent: 页面信息",{pageNumber:n.pageNumber,startPosition:n.startPosition,endPosition:n.endPosition,contentLength:((d=n.content)==null?void 0:d.length)||0}),this.bookContent.currentPage=t,this.bookContent.progress=Gt(n.startPosition,this.originalText.length),(u=(r=this.events).onPageChange)==null||u.call(r,t),(c=(i=this.events).onProgressChange)==null||c.call(i,this.bookContent.progress);const a=this.formatPageContent(n.content);return console.log(`TxtReader.getPageContent: 格式化后内容长度 ${(a==null?void 0:a.length)||0}`),a}async goToPage(t){await this.getPageContent(t)}async nextPage(){if(!this.bookContent||!this.paginationResult)return!1;const t=this.bookContent.currentPage+1;return t<=this.paginationResult.totalPages?(await this.goToPage(t),!0):!1}async previousPage(){if(!this.bookContent)return!1;const t=this.bookContent.currentPage-1;return t>=1?(await this.goToPage(t),!0):!1}async search(t){if(!this.originalText||!this.paginationResult)return[];const n=[],a=new RegExp(t,"gi");let d;for(;(d=a.exec(this.originalText))!==null;){const r=d.index,u=Oe(r,this.paginationResult.pages),i=Math.max(0,r-50),c=Math.min(this.originalText.length,r+t.length+50),b=this.originalText.substring(i,c);n.push({text:d[0],page:u,position:r,context:b})}return n}getCurrentPosition(){if(!this.bookContent||!this.paginationResult)return{page:1,characterPosition:0,scrollPosition:0};const t=et(this.bookContent.currentPage,this.paginationResult.pages);return{page:this.bookContent.currentPage,characterPosition:(t==null?void 0:t.startPosition)||0,scrollPosition:0}}async setPosition(t){if(this.paginationResult){if(t.page)await this.goToPage(t.page);else if(t.characterPosition!==void 0){const n=Oe(t.characterPosition,this.paginationResult.pages);await this.goToPage(n)}}}applySettings(t){if(this.currentSettings={...this.currentSettings,...t},this.originalText&&(t.font||t.page)){const n=this.getCurrentPosition();this.paginationResult=Xe(this.originalText,this.currentSettings),this.bookContent&&(this.bookContent.totalPages=this.paginationResult.totalPages,this.setPosition(n))}}getChapters(){var t;return((t=this.bookContent)==null?void 0:t.chapters)||[]}async goToChapter(t){const a=this.getChapters().find(d=>d.id===t);if(a&&this.paginationResult){const d=Oe(a.startPosition,this.paginationResult.pages);await this.goToPage(d)}}setEventListeners(t){this.events={...this.events,...t}}async getFullContent(){if(!this.originalText)throw new Error("书籍未加载");return this.originalText.replace(/\r\n/g,`
`).replace(/\r/g,`
`).trim()}dispose(){this.bookContent=null,this.originalText="",this.paginationResult=null,this.events={},this.encodingResult=null}async readFileContent(t){try{if(typeof window<"u"&&window.electronAPI){const r=window.electronAPI;if(r.txtReader&&r.txtReader.readFile){const u=await r.txtReader.readFile(t);return{content:u.content,encoding:u.encoding||"utf-8",size:u.size||u.content.length}}if(r.invoke){const u=await r.invoke("txt-reader:read-file",t);return{content:u.content,encoding:u.encoding||"utf-8",size:u.size||u.content.length}}if(r.txtReader&&r.txtReader.detectEncoding){const u=await r.txtReader.detectEncoding(t),i=await r.txtReader.readFile(t,u.encoding);return{content:i.content,encoding:i.encoding,size:i.size}}if(r.file&&r.file.read){const u=await r.file.read(t),i=u.buffer||u;return{content:new TextDecoder("utf-8",{fatal:!1}).decode(i),encoding:"utf-8",size:i.byteLength}}}const n=await fetch(t);if(!n.ok)throw new Error(`Failed to read file: ${n.statusText}`);const a=await n.arrayBuffer(),d=await detectEncoding(a);return{content:d.text,encoding:d.encoding,size:a.byteLength}}catch(n){throw new Error(`无法读取文件 ${t}: ${n}`)}}async readFile(t){const n=await this.readFileContent(t);return new TextEncoder().encode(n.content).buffer}generateBookId(t){return`txt_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}extractTitle(t){return(t.split(/[/\\]/).pop()||"").replace(/\.[^/.]+$/,"")||"未命名文档"}detectChapters(t){const n=[],a=t.split(`
`),d=[/^第[一二三四五六七八九十\d]+章\s*.*/,/^第[一二三四五六七八九十\d]+节\s*.*/,/^Chapter\s+\d+.*/i,/^\d+\.\s*.{1,50}$/,/^[一二三四五六七八九十]+、.*/];let r=0,u=1;for(let i=0;i<a.length;i++){const c=a[i].trim();c.length>0&&d.some(z=>z.test(c))&&(n.push({id:`chapter_${u}`,title:c,startPosition:r,endPosition:r+c.length,level:1}),u++),r+=a[i].length+1}return n.length===0&&n.push({id:"chapter_1",title:"正文",startPosition:0,endPosition:t.length,level:1}),n}detectLanguage(t){const n=/[\u4e00-\u9fff]/,a=/[a-zA-Z]/,d=t.match(n),r=t.match(a),u=d?d.length:0,i=r?r.length:0;return u>i?"zh-CN":i>0?"en":"unknown"}formatPageContent(t){return`<p>${t.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#39;").replace(/\n\n+/g,"</p><p>").replace(/\n/g,"<br>")}</p>`}}const Se=new Map,at=Tt("reader",()=>{const w=f(null),t=f(!1),n=f(null),a=f(null),d=f([]),r=f(!1),u=At({font:{family:"Microsoft YaHei, SimSun, serif",size:16,weight:"normal",lineHeight:1.6},page:{width:800,height:600,marginTop:40,marginBottom:40,marginLeft:60,marginRight:60},theme:{mode:"light",backgroundColor:"#ffffff",textColor:"#333333"},readingMode:"pagination",zoomLevel:1}),i=f([]),c=f(-1),b=f({}),z=oe(()=>w.value!==null),N=oe(()=>{var s;return((s=w.value)==null?void 0:s.currentPage)||1}),M=oe(()=>{var s;return((s=w.value)==null?void 0:s.totalPages)||0}),O=oe(()=>{var s;return((s=w.value)==null?void 0:s.progress)||0}),$=oe(()=>{var s;return((s=w.value)==null?void 0:s.chapters)||[]}),V=oe(()=>c.value>0),P=oe(()=>c.value<i.value.length-1);function y(s){if(!Se.has(s))switch(s){case"txt":Se.set(s,new Wt(u));break;case"pdf":Se.set(s,{loadBook:()=>Promise.resolve(null),setEventListeners:()=>{},applySettings:()=>{},dispose:()=>{}});break;default:throw new Error(`不支持的阅读器类型: ${s}`)}return Se.get(s)}async function B(s,I){t.value=!0,n.value=null;try{const Q=y(I);a.value=Q,Q.setEventListeners({onPageChange:ne=>{w.value&&(w.value.currentPage=ne)},onProgressChange:ne=>{w.value&&(w.value.progress=ne)},onLoadComplete:ne=>{w.value=ne,F()},onError:ne=>{n.value=ne.message}}),Q.applySettings(u);const X=await Q.loadBook(s);w.value=X,d.value=[],i.value=[],c.value=-1,F()}catch(Q){throw n.value=Q instanceof Error?Q.message:"加载书籍失败",Q}finally{t.value=!1}}async function G(s){if(a.value)try{await a.value.goToPage(s),F()}catch(I){n.value=I instanceof Error?I.message:"跳转页面失败"}}async function x(){if(!a.value)return!1;try{const s=await a.value.nextPage();return s&&F(),s}catch(s){return n.value=s instanceof Error?s.message:"翻页失败",!1}}async function D(){if(!a.value)return!1;try{const s=await a.value.previousPage();return s&&F(),s}catch(s){return n.value=s instanceof Error?s.message:"翻页失败",!1}}async function q(s){if(!a.value||!s.trim()){d.value=[];return}r.value=!0;try{const I=await a.value.search(s.trim());d.value=I}catch(I){n.value=I instanceof Error?I.message:"搜索失败",d.value=[]}finally{r.value=!1}}async function R(s){if(a.value)try{await a.value.setPosition({page:s.page,characterPosition:s.position,scrollPosition:0}),F()}catch(I){n.value=I instanceof Error?I.message:"跳转失败"}}async function Y(s){if(a.value)try{await a.value.goToChapter(s),F()}catch(I){n.value=I instanceof Error?I.message:"跳转章节失败"}}function L(s){Object.assign(u,s),a.value&&a.value.applySettings(u)}function F(){if(!a.value)return;const s=a.value.getCurrentPosition();c.value<i.value.length-1&&(i.value=i.value.slice(0,c.value+1)),i.value.push(s),c.value=i.value.length-1,i.value.length>50&&(i.value=i.value.slice(-50),c.value=i.value.length-1)}async function ie(){if(!V.value||!a.value)return;c.value--;const s=i.value[c.value];try{await a.value.setPosition(s)}catch(I){n.value=I instanceof Error?I.message:"后退失败",c.value++}}async function re(){if(!P.value||!a.value)return;c.value++;const s=i.value[c.value];try{await a.value.setPosition(s)}catch(I){n.value=I instanceof Error?I.message:"前进失败",c.value--}}function se(s,I){b.value[s]=I}function fe(s){return b.value[s]||null}function ce(){n.value=null}function h(){a.value&&a.value.dispose(),w.value=null,a.value=null,d.value=[],i.value=[],c.value=-1,n.value=null}return{currentBook:w,isLoading:t,error:n,searchResults:d,isSearching:r,settings:u,isBookLoaded:z,currentPage:N,totalPages:M,progress:O,chapters:$,canGoBack:V,canGoForward:P,loadBook:B,goToPage:G,nextPage:x,previousPage:D,searchText:q,goToSearchResult:R,goToChapter:Y,updateSettings:L,goBack:ie,goForward:re,saveProgress:se,getProgress:fe,clearError:ce,closeBook:h,currentReader:a}}),jt={class:"embedded-txt-reader"},Kt={class:"reader-toolbar"},Yt={class:"toolbar-left"},Zt={key:0,class:"book-info"},Qt={class:"book-title"},qt={class:"reading-progress"},Jt={class:"toolbar-center"},Xt={class:"ai-buttons-group"},eo={class:"toolbar-right"},to={class:"reader-body"},oo={key:0,class:"search-panel full-height"},no={class:"panel-header"},lo={class:"search-content"},ao={class:"search-input-wrapper"},so={key:0,class:"search-results-header"},io={class:"search-navigation"},ro={class:"search-index"},co={key:1,class:"search-results-list"},uo=["onClick"],vo={class:"result-index"},go={class:"result-text"},fo={key:0,class:"more-results"},po={key:2,class:"no-results"},ho={key:3,class:"search-tips"},mo={key:1,class:"toc-panel full-height"},wo={class:"panel-header"},_o={class:"toc-content"},ko={key:0,class:"toc-loading"},Po={key:1,class:"toc-empty"},yo={key:2,class:"toc-tree"},bo=["onClick"],Co={class:"toc-title"},$o={key:0,class:"toc-progress"},xo={key:2,class:"settings-panel full-height"},Io={class:"panel-header"},Ro={class:"settings-content"},To={class:"setting-group"},Ao={class:"setting-group"},Eo={class:"setting-group"},So={class:"setting-group"},Uo={class:"setting-note"},Bo={key:3,class:"ai-guide-panel full-height"},zo={class:"panel-header"},Lo={class:"ai-panel-content"},Vo={class:"ai-panel-description"},Fo={key:4,class:"editor-panel full-height"},Mo={class:"panel-header"},Do={class:"ai-panel-content"},Ho={class:"ai-panel-description"},No={key:5,class:"ai-chat-panel full-height"},Oo={class:"panel-header"},Go={class:"ai-panel-content"},Wo={class:"ai-panel-description"},jo={key:6,class:"ai-explore-panel full-height"},Ko={class:"panel-header"},Yo={class:"ai-panel-content"},Zo={class:"ai-panel-description"},Qo={key:7,class:"ai-evaluation-panel full-height"},qo={class:"panel-header"},Jo={class:"ai-panel-content"},Xo={class:"ai-panel-description"},en=["innerHTML"],tn={key:0,class:"loading-overlay"},on={key:1,class:"error-overlay"},nn={class:"error-text"},ln=Ve({__name:"EmbeddedTxtReader",props:{bookId:{default:""}},setup(w){const t=w,n=je(),a=at(),d=Ft(),r=f(),u=f(),i=f(!1),c=f(!1),b=f(!1),z=f(!1),N=f(!1),M=f(!1),O=f(!1),$=f(!1),V=f(""),P=f([]),y=f(-1),B=f(""),G=f(0),x=f(0),D=f([]),q=f(!1),R=f(""),Y=f(""),L=f({font:{family:"Microsoft YaHei",size:16,lineHeight:1.6}}),F=Et(),{currentBook:ie,isLoading:re,error:se}=a,fe=oe(()=>{const v=F.currentTheme;return v?v.name:"未知"}),ce=oe(()=>i.value||b.value||c.value||z.value||N.value||M.value||O.value||$.value),h=oe(()=>({fontFamily:L.value.font.family,fontSize:`${L.value.font.size}px`,lineHeight:L.value.font.lineHeight,height:"100%",overflowY:"auto",padding:"20px",whiteSpace:"pre-wrap"})),s=oe(()=>{if(x.value===0)return"0%";const v=Math.round(G.value/x.value*100);return`${Math.min(v,100)}%`}),I=oe(()=>{if(!B.value)return"";let v=B.value;if(P.value.length>0&&V.value){const o=new RegExp(`(${E(V.value)})`,"gi");v=v.replace(o,'<mark class="search-highlight">$1</mark>')}return v}),Q=()=>{he(),n.push("/bookshelf/library")},X=()=>{if(!V.value.trim()){P.value=[],y.value=-1;return}Re(V.value)},ne=()=>{V.value.trim()||(P.value=[],y.value=-1)},Re=v=>{const o=B.value,p=new RegExp(E(v),"gi"),A=[];let S;for(;(S=p.exec(o))!==null;)A.push({index:A.length,text:S[0],position:S.index});P.value=A,y.value=A.length>0?0:-1,A.length>0&&te(0)},De=()=>{y.value>0&&(y.value--,te(y.value))},T=()=>{y.value<P.value.length-1&&(y.value++,te(y.value))},g=()=>{V.value="",P.value=[],y.value=-1},le=v=>{y.value=v,te(v)},W=v=>{const o=B.value,p=Math.max(0,v.position-20),A=Math.min(o.length,v.position+v.text.length+20),S=o.substring(p,A),j=new RegExp(`(${E(V.value)})`,"gi");return S.replace(j,"【$1】")},te=v=>{if(!r.value||v<0||v>=P.value.length)return;const o=P.value[v],p=B.value,A=o.position,S=p.length,j=A/S,ae=r.value,H=(ae.scrollHeight-ae.clientHeight)*j;ae.scrollTo({top:H,behavior:"smooth"}),G.value=H,console.log(`跳转到第 ${v+1} 个搜索结果，位置: ${A}/${S} (${(j*100).toFixed(1)}%)`)},ee=v=>{const o=v.target;G.value=o.scrollTop,x.value=o.scrollHeight-o.clientHeight,$t(),He()},we=()=>{const v=window.getSelection();v&&v.toString().trim()&&console.log("Selected text:",v.toString())},pe=()=>{ke(()=>{r.value&&(x.value=r.value.scrollHeight-r.value.clientHeight)})},he=()=>{if(t.bookId&&x.value>0){const v=G.value/x.value;a.saveProgress(t.bookId,{scrollPosition:G.value,progress:Math.min(v,1),timestamp:Date.now()})}};let de=null;const He=()=>{de&&clearTimeout(de),de=setTimeout(he,1e3)},xe=async()=>{if(t.bookId)try{const v=await d.getBook(t.bookId);if(!v)throw new Error("图书不存在");if(await a.loadBook(v.filePath,"txt"),a.currentReader){B.value=await a.currentReader.getFullContent(),await Te(v.filePath,v.title);const o=await a.getProgress(t.bookId);o&&r.value&&ke(()=>{r.value&&o.scrollPosition&&(r.value.scrollTop=o.scrollPosition)})}}catch(v){console.error("Failed to load book:",v),be.error("加载图书失败")}},Te=async(v,o)=>{try{let p=await Pt(v);p.length===0&&B.value&&(p=await yt(B.value,v,o)),D.value=p,p.length>0&&console.log(`Loaded ${p.length} TOC items for ${o}`)}catch(p){console.error("Failed to load/generate TOC:",p)}},Ae=()=>{a.clearError(),xe()},E=v=>v.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),_e=()=>{c.value?c.value=!1:(c.value=!0,b.value=!1,i.value=!1,ke(()=>{u.value&&u.value.focus()}))},Ne=()=>{b.value?b.value=!1:(b.value=!0,i.value=!1,c.value=!1)},st=()=>{i.value?i.value=!1:(i.value=!0,b.value=!1,c.value=!1)},it=()=>{c.value=!1},rt=()=>{b.value=!1},ct=()=>{i.value=!1},ut=()=>{z.value?z.value=!1:(z.value=!0,N.value=!1,M.value=!1,O.value=!1,$.value=!1,i.value=!1,b.value=!1,c.value=!1)},dt=()=>{N.value?N.value=!1:(N.value=!0,z.value=!1,M.value=!1,O.value=!1,$.value=!1,i.value=!1,b.value=!1,c.value=!1)},vt=()=>{M.value?M.value=!1:(M.value=!0,z.value=!1,N.value=!1,O.value=!1,$.value=!1,i.value=!1,b.value=!1,c.value=!1)},gt=()=>{O.value?O.value=!1:(O.value=!0,z.value=!1,N.value=!1,M.value=!1,$.value=!1,i.value=!1,b.value=!1,c.value=!1)},ft=()=>{$.value?$.value=!1:($.value=!0,z.value=!1,N.value=!1,M.value=!1,O.value=!1,i.value=!1,b.value=!1,c.value=!1)},pt=()=>{z.value=!1},ht=()=>{N.value=!1},mt=()=>{M.value=!1},wt=()=>{O.value=!1},_t=()=>{$.value=!1},kt=v=>{const o=v.split(`
`),p=[];let A=0;const S=[{regex:/^第[一二三四五六七八九十\d]+章\s*(.*)$/i,level:1},{regex:/^第[一二三四五六七八九十\d]+节\s*(.*)$/i,level:2},{regex:/^第[一二三四五六七八九十\d]+部分\s*(.*)$/i,level:1},{regex:/^第[一二三四五六七八九十\d]+篇\s*(.*)$/i,level:1},{regex:/^Chapter\s+(\d+)\s*[:\-\s]*(.*)$/i,level:1},{regex:/^Section\s+(\d+)\s*[:\-\s]*(.*)$/i,level:2},{regex:/^Part\s+(\d+)\s*[:\-\s]*(.*)$/i,level:1},{regex:/^(\d+)\.\s*(.+)$/i,level:1},{regex:/^(\d+)\.(\d+)\s*(.+)$/i,level:2},{regex:/^(\d+)\.(\d+)\.(\d+)\s*(.+)$/i,level:3},{regex:/^[【\[]第[一二三四五六七八九十\d]+章[】\]]\s*(.*)$/i,level:1},{regex:/^[【\[]第[一二三四五六七八九十\d]+节[】\]]\s*(.*)$/i,level:2},{regex:/^[★☆]\s*(.+)$/i,level:1},{regex:/^[■□]\s*(.+)$/i,level:2}];return o.forEach((j,ae)=>{var H;const ue=j.trim();if(!ue){A+=j.length+1;return}for(const ve of S){const Ee=ue.match(ve.regex);if(Ee){let Ie="";ve.regex.source.includes("(.*)")?Ie=((H=Ee[Ee.length-1])==null?void 0:H.trim())||ue:Ie=ue,Ie||(Ie=ue);const xt=`chapter-${p.length+1}-${ae}`;p.push({id:xt,title:Ie,level:ve.level,position:A,lineNumber:ae+1});break}}A+=j.length+1}),p},Ke=v=>{const o=Math.max(v.lastIndexOf("/"),v.lastIndexOf("\\")),p=o>=0?v.substring(0,o):"",A=o>=0?v.substring(o+1):v,S=A.lastIndexOf("."),j=S>=0?A.substring(0,S):A;return p?`${p}/${j}_toc.json`:`${j}_toc.json`},Pt=async v=>{try{const o=Ke(v);if(Y.value=o,await window.electronAPI.file.exists(o)){const A=await window.electronAPI.file.read(o),S=new TextDecoder("utf-8").decode(A);return JSON.parse(S).items||[]}return[]}catch(o){return console.error("Failed to load table of contents:",o),[]}},yt=async(v,o,p)=>{try{q.value=!0;const A=kt(v);if(A.length>0){const S={bookId:t.bookId||"",bookTitle:p,generatedAt:Date.now(),items:A},j=Ke(o);console.log(`Generated TOC for ${p}, would save to: ${j}`),console.log(`Generated TOC with ${A.length} items for ${p}`)}return A}catch(A){return console.error("Failed to generate table of contents:",A),[]}finally{q.value=!1}},bt=v=>{if(r.value)try{const A=B.value.substring(0,v.position).split(`
`),S=parseFloat(getComputedStyle(r.value).lineHeight)||24,j=(A.length-1)*S;r.value.scrollTop=j,R.value=v.id,Ct(v.title)}catch(o){console.error("Failed to jump to chapter:",o),be.error("跳转失败")}},Ct=v=>{const o=new RegExp(`(${E(v)})`,"gi");B.value.replace(o,'<mark class="chapter-highlight">$1</mark>'),ke(()=>{setTimeout(()=>{},3e3)})},$t=()=>{if(D.value.length===0||x.value===0)return;const v=G.value/x.value;D.value.forEach((p,A)=>{const S=D.value[A+1],j=S?p.position/B.value.length:1;if(v>=j)p.progress=100;else if(v>=p.position/B.value.length){const ae=S?S.position-p.position:B.value.length-p.position,ue=(G.value*B.value.length/x.value-p.position)/ae;p.progress=Math.max(0,Math.min(100,ue*100))}else p.progress=0});const o=D.value.find(p=>{const A=p.position/B.value.length*x.value;return G.value>=A-100});o&&(R.value=o.id)};return Fe(()=>{xe(),ke(()=>{var v;(v=r.value)==null||v.focus()})}),Ge(()=>{he(),de&&clearTimeout(de)}),We(()=>se==null?void 0:se.value,v=>{v&&be.error(v)}),(v,o)=>{const p=Z("el-button"),A=Z("el-input"),S=Z("el-icon"),j=Z("el-slider"),ae=Z("el-option"),ue=Z("el-select");return m(),k("div",jt,[e("div",Kt,[e("div",Yt,[l(p,{type:"primary",icon:_(Be),onClick:Q,size:"default"},{default:C(()=>o[4]||(o[4]=[J(" 返回图书列表 ")])),_:1,__:[4]},8,["icon"]),_(ie)?(m(),k("div",Zt,[e("span",Qt,U(_(ie).title),1),e("span",qt,U(s.value),1)])):K("",!0)]),e("div",Jt,[e("div",Xt,[l(p,{icon:_(Ye),onClick:ut,size:"small",title:"AI学习导引 - 让专家在您身旁导引您，让您学习更加轻松",type:z.value?"primary":"default"},{default:C(()=>o[5]||(o[5]=[J(" AI学习导引 ")])),_:1,__:[5]},8,["icon","type"]),l(p,{icon:_(Ze),onClick:vt,size:"small",title:"AI学习对话 - 让每一次问答都成为思维的跳板，跃向更深邃的理解",type:M.value?"primary":"default"},{default:C(()=>o[6]||(o[6]=[J(" AI学习对话 ")])),_:1,__:[6]},8,["icon","type"]),l(p,{icon:_(Qe),onClick:gt,size:"small",title:"AI学习探究 - 让每一次追问都点亮新的知识星空",type:O.value?"primary":"default"},{default:C(()=>o[7]||(o[7]=[J(" AI学习探究 ")])),_:1,__:[7]},8,["icon","type"]),l(p,{icon:_(qe),onClick:ft,size:"small",title:"AI评测 - 智能评估学习效果，提供个性化学习建议",type:$.value?"primary":"default"},{default:C(()=>o[8]||(o[8]=[J(" AI评测 ")])),_:1,__:[8]},8,["icon","type"]),l(p,{icon:_(Je),onClick:dt,size:"small",title:"编辑器 - 提供文本编辑功能",type:N.value?"primary":"default"},{default:C(()=>o[9]||(o[9]=[J(" 笔记编辑器 ")])),_:1,__:[9]},8,["icon","type"])])]),e("div",eo,[l(p,{icon:_(Pe),onClick:_e,size:"small",title:"搜索文本",type:c.value?"primary":"default"},null,8,["icon","type"]),l(p,{icon:_(St),onClick:Ne,size:"small",title:"目录导航",type:b.value?"primary":"default"},null,8,["icon","type"]),l(p,{icon:_(tt),onClick:st,size:"small",title:"阅读设置",type:i.value?"primary":"default"},null,8,["icon","type"])])]),e("div",to,[e("div",{class:ye(["left-panel",{"panel-visible":ce.value}])},[c.value?(m(),k("div",oo,[e("div",no,[o[10]||(o[10]=e("h3",null,"搜索文本",-1)),l(p,{link:"",onClick:it,icon:_(ge),size:"small"},null,8,["icon"])]),e("div",lo,[e("div",ao,[l(A,{ref_key:"searchInputRef",ref:u,modelValue:V.value,"onUpdate:modelValue":o[0]||(o[0]=H=>V.value=H),placeholder:"搜索文本...",onKeyup:ot(X,["enter"]),onInput:ne,clearable:"",size:"small"},{append:C(()=>[l(p,{onClick:X,icon:_(Pe)},null,8,["icon"])]),_:1},8,["modelValue"])]),P.value.length>0?(m(),k("div",so,[e("span",null,"找到 "+U(P.value.length)+" 个结果",1),e("div",io,[l(p,{size:"small",disabled:y.value<=0,onClick:De,icon:_(Ut)},null,8,["disabled","icon"]),e("span",ro,U(y.value+1)+" / "+U(P.value.length),1),l(p,{size:"small",disabled:y.value>=P.value.length-1,onClick:T,icon:_(Bt)},null,8,["disabled","icon"]),l(p,{onClick:g,size:"small",icon:_(ge),title:"清除搜索"},null,8,["icon"])])])):K("",!0),P.value.length>0?(m(),k("div",co,[(m(!0),k(Ce,null,$e(P.value.slice(0,20),(H,ve)=>(m(),k("div",{key:ve,class:ye(["search-result-item",{active:ve===y.value}]),onClick:Ee=>le(ve)},[e("span",vo,U(ve+1),1),e("span",go,U(W(H)),1)],10,uo))),128)),P.value.length>20?(m(),k("div",fo," 还有 "+U(P.value.length-20)+" 个结果... ",1)):K("",!0)])):V.value&&P.value.length===0?(m(),k("div",po,[l(S,null,{default:C(()=>[l(_(Pe))]),_:1}),o[11]||(o[11]=e("span",null,"未找到匹配的内容",-1))])):(m(),k("div",ho,[l(S,null,{default:C(()=>[l(_(Pe))]),_:1}),o[12]||(o[12]=e("span",null,"输入关键词开始搜索",-1))]))])])):K("",!0),b.value?(m(),k("div",mo,[e("div",wo,[o[13]||(o[13]=e("h3",null,"目录",-1)),l(p,{link:"",onClick:rt,icon:_(ge),size:"small"},null,8,["icon"])]),e("div",_o,[q.value?(m(),k("div",ko,[l(S,{class:"loading-icon"},{default:C(()=>[l(_(ze))]),_:1}),o[14]||(o[14]=e("span",null,"正在生成目录...",-1))])):D.value.length===0?(m(),k("div",Po,[l(S,null,{default:C(()=>[l(_(nt))]),_:1}),o[15]||(o[15]=e("span",null,"未找到目录结构",-1))])):(m(),k("div",yo,[(m(!0),k(Ce,null,$e(D.value,H=>(m(),k("div",{key:H.id,class:ye(["toc-item",{"toc-level-1":H.level===1,"toc-level-2":H.level===2,"toc-level-3":H.level===3,active:R.value===H.id}]),onClick:ve=>bt(H)},[e("span",Co,U(H.title),1),H.progress!==void 0?(m(),k("span",$o,U(Math.round(H.progress))+"%",1)):K("",!0)],10,bo))),128))]))])])):K("",!0),i.value?(m(),k("div",xo,[e("div",Io,[o[16]||(o[16]=e("h3",null,"阅读设置",-1)),l(p,{link:"",onClick:ct,icon:_(ge),size:"small"},null,8,["icon"])]),e("div",Ro,[e("div",To,[o[17]||(o[17]=e("label",null,"字体大小",-1)),l(j,{modelValue:L.value.font.size,"onUpdate:modelValue":o[1]||(o[1]=H=>L.value.font.size=H),min:12,max:32,step:1,"show-input":"",onChange:pe},null,8,["modelValue"])]),e("div",Ao,[o[18]||(o[18]=e("label",null,"行间距",-1)),l(j,{modelValue:L.value.font.lineHeight,"onUpdate:modelValue":o[2]||(o[2]=H=>L.value.font.lineHeight=H),min:1,max:3,step:.1,"show-input":"",onChange:pe},null,8,["modelValue"])]),e("div",Eo,[o[19]||(o[19]=e("label",null,"字体族",-1)),l(ue,{modelValue:L.value.font.family,"onUpdate:modelValue":o[3]||(o[3]=H=>L.value.font.family=H),onChange:pe,style:{width:"100%"}},{default:C(()=>[l(ae,{label:"微软雅黑",value:"Microsoft YaHei"}),l(ae,{label:"宋体",value:"SimSun"}),l(ae,{label:"黑体",value:"SimHei"}),l(ae,{label:"楷体",value:"KaiTi"})]),_:1},8,["modelValue"])]),e("div",So,[o[20]||(o[20]=e("label",null,"主题模式",-1)),o[21]||(o[21]=e("p",{class:"setting-note"},"主题设置已移至全局设置，请使用侧边栏或菜单栏的主题切换功能",-1)),e("p",Uo,"当前主题: "+U(fe.value),1)])])])):K("",!0),z.value?(m(),k("div",Bo,[e("div",zo,[o[22]||(o[22]=e("h3",null,"AI学习导引",-1)),l(p,{link:"",onClick:pt,icon:_(ge),size:"small"},null,8,["icon"])]),e("div",Lo,[e("div",Vo,[l(S,null,{default:C(()=>[l(_(Ye))]),_:1}),o[23]||(o[23]=e("p",null,"让专家在您身旁导引您，让您学习更加轻松",-1))]),o[24]||(o[24]=e("div",{class:"ai-panel-placeholder"},[e("div",{class:"placeholder-content"},[e("h4",null,"AI学习导引功能"),e("p",null,"这里将显示AI学习导引的内容，包括："),e("ul",null,[e("li",null,"智能学习路径推荐"),e("li",null,"个性化学习建议"),e("li",null,"学习进度跟踪"),e("li",null,"专家级指导意见")])])],-1))])])):K("",!0),N.value?(m(),k("div",Fo,[e("div",Mo,[o[25]||(o[25]=e("h3",null,"编辑器",-1)),l(p,{link:"",onClick:ht,icon:_(ge),size:"small"},null,8,["icon"])]),e("div",Do,[e("div",Ho,[l(S,null,{default:C(()=>[l(_(Je))]),_:1}),o[26]||(o[26]=e("p",null,"提供文本编辑功能",-1))]),o[27]||(o[27]=e("div",{class:"ai-panel-placeholder"},[e("div",{class:"placeholder-content"},[e("h4",null,"编辑器功能"),e("p",null,"这里将显示编辑器的内容，包括："),e("ul",null,[e("li",null,"文本编辑和格式化"),e("li",null,"笔记记录和整理"),e("li",null,"内容标注和高亮"),e("li",null,"文档导出和分享")])])],-1))])])):K("",!0),M.value?(m(),k("div",No,[e("div",Oo,[o[28]||(o[28]=e("h3",null,"AI学习对话",-1)),l(p,{link:"",onClick:mt,icon:_(ge),size:"small"},null,8,["icon"])]),e("div",Go,[e("div",Wo,[l(S,null,{default:C(()=>[l(_(Ze))]),_:1}),o[29]||(o[29]=e("p",null,"让每一次问答都成为思维的跳板，跃向更深邃的理解",-1))]),o[30]||(o[30]=e("div",{class:"ai-panel-placeholder"},[e("div",{class:"placeholder-content"},[e("h4",null,"AI学习对话功能"),e("p",null,"这里将显示AI对话的内容，包括："),e("ul",null,[e("li",null,"智能问答交互"),e("li",null,"学习疑问解答"),e("li",null,"知识点深度探讨"),e("li",null,"个性化学习建议")])])],-1))])])):K("",!0),O.value?(m(),k("div",jo,[e("div",Ko,[o[31]||(o[31]=e("h3",null,"AI学习探究",-1)),l(p,{link:"",onClick:wt,icon:_(ge),size:"small"},null,8,["icon"])]),e("div",Yo,[e("div",Zo,[l(S,null,{default:C(()=>[l(_(Qe))]),_:1}),o[32]||(o[32]=e("p",null,"让每一次追问都点亮新的知识星空",-1))]),o[33]||(o[33]=e("div",{class:"ai-panel-placeholder"},[e("div",{class:"placeholder-content"},[e("h4",null,"AI学习探究功能"),e("p",null,"这里将显示AI探究的内容，包括："),e("ul",null,[e("li",null,"深度知识挖掘"),e("li",null,"关联概念探索"),e("li",null,"学习路径拓展"),e("li",null,"创新思维启发")])])],-1))])])):K("",!0),$.value?(m(),k("div",Qo,[e("div",qo,[o[34]||(o[34]=e("h3",null,"AI评测",-1)),l(p,{link:"",onClick:_t,icon:_(ge),size:"small"},null,8,["icon"])]),e("div",Jo,[e("div",Xo,[l(S,null,{default:C(()=>[l(_(qe))]),_:1}),o[35]||(o[35]=e("p",null,"智能评估学习效果，提供个性化学习建议",-1))]),o[36]||(o[36]=zt('<div class="ai-panel-placeholder" data-v-02a52042><div class="placeholder-content" data-v-02a52042><h4 data-v-02a52042>AI评测功能</h4><p data-v-02a52042>这里将显示AI评测的内容，包括：</p><ul data-v-02a52042><li data-v-02a52042>学习效果智能评估</li><li data-v-02a52042>知识掌握程度分析</li><li data-v-02a52042>个性化学习建议</li><li data-v-02a52042>学习进度跟踪报告</li><li data-v-02a52042>薄弱环节识别</li><li data-v-02a52042>学习路径优化建议</li></ul><div class="evaluation-preview" data-v-02a52042><h5 data-v-02a52042>评测维度</h5><div class="evaluation-metrics" data-v-02a52042><div class="metric-item" data-v-02a52042><span class="metric-label" data-v-02a52042>理解深度</span><div class="metric-bar" data-v-02a52042><div class="metric-progress" style="width:75%;" data-v-02a52042></div></div><span class="metric-value" data-v-02a52042>75%</span></div><div class="metric-item" data-v-02a52042><span class="metric-label" data-v-02a52042>知识关联</span><div class="metric-bar" data-v-02a52042><div class="metric-progress" style="width:60%;" data-v-02a52042></div></div><span class="metric-value" data-v-02a52042>60%</span></div><div class="metric-item" data-v-02a52042><span class="metric-label" data-v-02a52042>应用能力</span><div class="metric-bar" data-v-02a52042><div class="metric-progress" style="width:80%;" data-v-02a52042></div></div><span class="metric-value" data-v-02a52042>80%</span></div></div></div></div></div>',1))])])):K("",!0)],2),e("div",{class:ye(["reader-main",{"with-left-panel":ce.value}])},[e("div",{class:"reader-content",style:lt(h.value),ref_key:"contentRef",ref:r,onScroll:ee,tabindex:"0"},[e("div",{class:"text-content",innerHTML:I.value,onMouseup:we},null,40,en)],36)],2)]),_(re)?(m(),k("div",tn,[l(S,{class:"loading-icon",size:40},{default:C(()=>[l(_(ze))]),_:1}),o[37]||(o[37]=e("div",{class:"loading-text"},"正在加载文档...",-1))])):K("",!0),_(se)?(m(),k("div",on,[l(S,{class:"error-icon"},{default:C(()=>[l(_(Le))]),_:1}),e("div",nn,U(_(se)),1),l(p,{type:"primary",onClick:Ae},{default:C(()=>o[38]||(o[38]=[J("重试")])),_:1,__:[38]})])):K("",!0)])}}}),an=Me(ln,[["__scopeId","data-v-02a52042"]]);typeof URL<"u"&&!URL.parse&&(URL.parse=function(w,t){try{return new URL(w,t)}catch{return null}});const sn={class:"pdf-reader-view"},rn={class:"toolbar"},cn={class:"toolbar-left"},un={key:0,class:"book-info"},dn={class:"book-title"},vn={class:"reading-progress"},gn={class:"content-area"},fn={key:0,class:"sidebar"},pn={key:0,class:"thumbnails-panel"},hn={class:"thumbnail-list"},mn=["onClick"],wn={class:"thumbnail-page"},_n={key:1,class:"bookmarks-panel"},kn={class:"bookmark-list"},Pn=["onClick"],yn={class:"bookmark-title"},bn={class:"bookmark-page"},Cn={class:"pdf-viewer",ref:"viewerContainer"},$n={key:0,class:"loading-container"},xn={key:1,class:"error-container"},In={key:2,class:"pdf-container"},Rn=["src"],Tn=Ve({__name:"PdfReaderView",props:{bookId:{}},emits:["go-back"],setup(w,{emit:t}){const n=w,a=t;je();const d=f(!0),r=f(""),u=f(1),i=f(0),c=f(1),b=f(!1),z=f("bookmarks"),N=f([]),M=f(""),O=f(),$=f(""),V=async h=>{if(console.log("原始文件路径:",h),h.startsWith("http")||h.startsWith("blob:"))return h;try{console.log("通过IPC读取PDF文件:",h);const s=await window.electronAPI.file.read(h),I=new Blob([s],{type:"application/pdf"}),Q=URL.createObjectURL(I);return console.log("转换后的Blob URL:",Q),Q}catch(s){throw console.error("读取PDF文件失败:",s),new Error(`无法读取PDF文件: ${s.message}`)}};We(M,async h=>{if(console.log("pdfFilePath changed:",h),$.value&&(console.log("清理之前的Blob URL:",$.value),URL.revokeObjectURL($.value),$.value=""),h)try{d.value=!0,r.value="",console.log("PDF文档开始加载:",h);const s=await V(h);O.value=s,$.value=s,setTimeout(()=>{d.value&&(console.log("PDF加载超时，但继续显示"),d.value=!1)},5e3)}catch(s){console.error("PDF加载错误:",s),r.value=`PDF加载失败: ${s.message||s}`,d.value=!1}});const P=()=>{a("go-back")},y=()=>{u.value>1&&(u.value--,Y())},B=()=>{u.value<i.value&&(u.value++,Y())},G=h=>{u.value=h,Y()},x=()=>{c.value=Math.min(c.value*1.25,5)},D=()=>{c.value=Math.max(c.value/1.25,.25)},q=h=>{console.log("PDF iframe加载完成:",h),d.value=!1,i.value===0&&(i.value=1),F()},R=h=>{console.error("PDF iframe加载失败:",h),h.value="PDF加载失败: 无法显示PDF文件",d.value=!1},Y=async()=>{var h,s;if((s=(h=window.electronAPI)==null?void 0:h.reader)!=null&&s.updateProgress){const I=u.value/i.value*100;await window.electronAPI.reader.updateProgress(n.bookId,I,u.value)}},L=()=>{r.value="",ie()},F=async()=>{try{const h=await window.electronAPI.reader.getProgress(n.bookId);h&&h.currentPage&&(u.value=h.currentPage)}catch(h){console.error("恢复阅读进度失败:",h)}},ie=async()=>{try{console.log("开始加载PDF文档，书籍ID:",n.bookId),d.value=!0,r.value="";const h=await window.electronAPI.reader.getBook(n.bookId);if(console.log("获取到书籍信息:",h),!h)throw new Error("书籍不存在");const s=h.file_path||h.filePath;if(console.log("设置PDF文件路径:",s),!s)throw new Error("PDF文件路径为空");M.value=s,await re(),console.log("PDF文档路径设置完成:",M.value)}catch(h){console.error("加载PDF失败:",h),r.value=`加载失败: ${h.message||h}`,d.value=!1}},re=async()=>{console.log("书签功能已禁用")},se=h=>{if(!(h.target instanceof HTMLInputElement))switch(h.key){case"ArrowLeft":case"PageUp":h.preventDefault(),y();break;case"ArrowRight":case"PageDown":case" ":h.preventDefault(),B();break;case"+":case"=":h.preventDefault(),x();break;case"-":h.preventDefault(),D();break;case"0":h.preventDefault(),resetZoom();break;case"f":case"F":(h.ctrlKey||h.metaKey)&&(h.preventDefault(),toggleSearch());break;case"Escape":showSearch.value&&(h.preventDefault(),toggleSearch());break}},fe=at(),{currentBook:ce}=fe;return Fe(()=>{ie(),document.addEventListener("keydown",se)}),Ge(()=>{document.removeEventListener("keydown",se),$.value&&(console.log("组件卸载时清理Blob URL:",$.value),URL.revokeObjectURL($.value),$.value="")}),(h,s)=>{const I=Z("el-button"),Q=Z("el-icon");return m(),k("div",sn,[e("div",rn,[e("div",cn,[l(I,{onClick:P,type:"primary",icon:_(Be)},{default:C(()=>s[0]||(s[0]=[J(" 返回图书列表 ")])),_:1,__:[0]},8,["icon"])]),_(ce)?(m(),k("div",un,[e("span",dn,U(_(ce).title),1),e("span",vn,U(h.progressText),1)])):K("",!0)]),e("div",gn,[b.value?(m(),k("div",fn,[z.value==="thumbnails"?(m(),k("div",pn,[s[1]||(s[1]=e("h3",null,"缩略图",-1)),e("div",hn,[(m(!0),k(Ce,null,$e(i.value,X=>(m(),k("div",{key:X,class:ye(["thumbnail-item",{active:X===u.value}]),onClick:ne=>G(X)},[e("div",wn,U(X),1)],10,mn))),128))])])):(m(),k("div",_n,[s[2]||(s[2]=e("h3",null,"书签",-1)),e("div",kn,[(m(!0),k(Ce,null,$e(N.value,X=>(m(),k("div",{key:X.id,class:"bookmark-item",onClick:ne=>h.goToBookmark(X)},[e("div",yn,U(X.title),1),e("div",bn,"第 "+U(X.pageNumber)+" 页",1)],8,Pn))),128))])]))])):K("",!0),e("div",Cn,[d.value?(m(),k("div",$n,[l(Q,{class:"is-loading"},{default:C(()=>[l(_(ze))]),_:1}),s[3]||(s[3]=e("span",null,"正在加载PDF文档...",-1))])):r.value?(m(),k("div",xn,[l(Q,null,{default:C(()=>[l(_(Le))]),_:1}),e("span",null,U(r.value),1),l(I,{onClick:L,type:"primary",size:"small"},{default:C(()=>s[4]||(s[4]=[J("重试")])),_:1,__:[4]})])):O.value?(m(),k("div",In,[e("iframe",{src:O.value,class:"pdf-iframe",onLoad:q,onError:R},null,40,Rn)])):K("",!0)],512)])])}}}),An=Me(Tn,[["__scopeId","data-v-4896e813"]]),En={class:"epub-reader-container"},Sn={key:0,class:"loading-container"},Un={class:"loading-content"},Bn={class:"loading-text"},zn={key:0,class:"error-text"},Ln={class:"loading-actions"},Vn={key:1,class:"reader-main"},Fn={class:"reader-toolbar"},Mn={class:"toolbar-left"},Dn={class:"book-title"},Hn={class:"toolbar-center"},Nn={class:"chapter-progress-info"},On={class:"chapter-info"},Gn={class:"reading-progress"},Wn={class:"toolbar-right"},jn={class:"reader-content",ref:"contentContainer"},Kn={class:"chapter-container"},Yn={class:"chapter-title"},Zn=["innerHTML"],Qn={class:"toc-container"},qn=["onClick"],Jn={class:"toc-title"},Xn={class:"toc-level"},el={class:"search-container"},tl={key:0,class:"search-results"},ol={class:"results-count"},nl=["onClick"],ll={class:"result-context"},al={class:"result-info"},sl={key:1,class:"no-results"},il={class:"settings-container"},rl={class:"setting-group"},cl={class:"setting-item"},ul={class:"setting-item"},dl={class:"setting-group"},vl={class:"setting-group"},gl={class:"setting-item"},fl=Ve({__name:"EpubReaderView",props:{bookId:{}},emits:["go-back"],setup(w,{emit:t}){const n=w,a=t,d=f(!0),r=f(!1),u=f(""),i=f(0),c=f("正在加载..."),b=f(""),z=f(null),N=f(null),M=f([]),O=f(null),$=f(0),V=f(0),P=f(0),y=f(!1),B=f(!1),G=f(!1),x=f(""),D=f([]),q=f(!1),R=f({fontSize:16,lineHeight:1.6,theme:"light",maxWidth:800,fontFamily:"system-ui, -apple-system, sans-serif"}),Y=oe(()=>({fontSize:`${R.value.fontSize}px`,lineHeight:R.value.lineHeight,maxWidth:`${R.value.maxWidth}px`,fontFamily:R.value.fontFamily,margin:"0 auto",padding:"20px",backgroundColor:L().background,color:L().text}));function L(){return{light:{background:"#ffffff",text:"#333333"},dark:{background:"#1a1a1a",text:"#e0e0e0"},sepia:{background:"#f7f3e9",text:"#5c4b37"}}[R.value.theme]}Fe(async()=>{await F()}),Ge(()=>{De()});async function F(){try{if(d.value=!0,u.value="",i.value=0,c.value="正在初始化...",console.log(`EpubReaderView: 开始加载书籍 ${n.bookId}`),await ie(),i.value=10,c.value="获取书籍信息...",z.value=await window.electronAPI.reader.getBook(n.bookId),!z.value)throw new Error("未找到指定的书籍");i.value=30,c.value="准备EPUB阅读器...";const T=z.value.file_path||z.value.filePath;if(!T)throw new Error(`书籍文件路径无效: ${T}`);const g=`epub-reader-${n.bookId}-${Date.now()}`;b.value=g,i.value=50,c.value="创建阅读器实例...";const le={enableImageLoading:!0,enableStyleProcessing:!0,enableToc:!0,enableSearch:!0},W=await window.electronAPI.epubReader.createReader(g,T,le);if(!(W!=null&&W.success))throw new Error((W==null?void 0:W.error)||"创建阅读器失败");i.value=70,c.value="加载目录信息...";const te=await window.electronAPI.epubReader.getToc(g);if(te.success){const ee=te.toc||te.chapters||[];ee.length>0&&ee[0].children?M.value=ee[0].children:M.value=ee,N.value=te.bookInfo,V.value=M.value.length,console.log(`EpubReaderView: 加载目录完成，章节数: ${V.value}`),console.log("EpubReaderView: 目录项:",M.value)}i.value=90,c.value="加载第一章...",V.value>0&&await re(0),i.value=100,c.value="加载完成",await new Promise(ee=>setTimeout(ee,500)),d.value=!1,r.value=!0,console.log("EpubReaderView: EPUB书籍加载完成")}catch(T){console.error("EpubReaderView: 加载失败:",T),u.value=T instanceof Error?T.message:"加载失败",c.value="加载失败"}}async function ie(T=10,g=100){var le,W,te,ee;for(let we=0;we<T;we++){if((W=(le=window.electronAPI)==null?void 0:le.reader)!=null&&W.getBook&&((ee=(te=window.electronAPI)==null?void 0:te.epubReader)!=null&&ee.createReader))return;await new Promise(pe=>setTimeout(pe,g))}throw new Error("EPUB阅读器API初始化超时，请稍后重试")}async function re(T){try{if(!b.value)throw new Error("阅读器未初始化");const g=await window.electronAPI.epubReader.getChapter(b.value,T);if(g!=null&&g.success&&g.chapter){O.value=g.chapter,$.value=T,P.value=V.value>0?(T+1)/V.value*100:0,await ke();const le=document.querySelector(".reader-content");le&&(le.scrollTop=0),await X()}else throw new Error((g==null?void 0:g.error)||"获取章节内容失败")}catch(g){console.error("加载章节失败:",g),be.error("加载章节失败")}}async function se(){$.value>0&&await re($.value-1)}async function fe(){$.value<V.value-1&&await re($.value+1)}async function ce(T){T>=0&&T<V.value&&(await re(T),y.value=!1)}async function h(){if(!x.value.trim()||!b.value){D.value=[];return}try{q.value=!0,console.log("EpubReaderView: 执行搜索:",x.value.trim());const T=await window.electronAPI.epubReader.search(b.value,x.value.trim());D.value=T||[],D.value.length===0&&be.info("未找到匹配的内容")}catch(T){console.error("搜索失败:",T),be.error("搜索失败")}finally{q.value=!1}}async function s(T){await ce(T.chapterIndex),B.value=!1}function I(){}function Q(T){T.target.tagName==="A"&&T.preventDefault()}async function X(){var T,g;if((g=(T=window.electronAPI)==null?void 0:T.reader)!=null&&g.updateProgress&&z.value)try{await window.electronAPI.reader.updateProgress(n.bookId,P.value,$.value+1)}catch(le){console.warn("保存阅读进度失败:",le)}}async function ne(){u.value="",await F()}function Re(){a("go-back")}function De(){b.value&&window.electronAPI.epubReader.destroyReader(b.value).catch(T=>console.warn("清理阅读器失败:",T))}return(T,g)=>{var xe,Te,Ae;const le=Z("el-progress"),W=Z("el-button"),te=Z("el-card"),ee=Z("el-drawer"),we=Z("el-input"),pe=Z("el-empty"),he=Z("el-slider"),de=Z("el-radio"),He=Z("el-radio-group");return m(),k("div",En,[d.value?(m(),k("div",Sn,[l(te,{class:"loading-card"},{default:C(()=>[e("div",Un,[l(le,{percentage:i.value,status:u.value?"exception":"success","stroke-width":8},null,8,["percentage","status"]),e("p",Bn,U(c.value),1),u.value?(m(),k("p",zn,U(u.value),1)):K("",!0),e("div",Ln,[l(W,{onClick:Re},{default:C(()=>g[11]||(g[11]=[J("返回书库")])),_:1,__:[11]}),u.value?(m(),Ue(W,{key:0,onClick:ne,type:"primary"},{default:C(()=>g[12]||(g[12]=[J("重试")])),_:1,__:[12]})):K("",!0)])])]),_:1})])):r.value?(m(),k("div",Vn,[e("div",Fn,[e("div",Mn,[l(W,{onClick:Re,icon:_(Be),circle:""},null,8,["icon"]),e("span",Dn,U(((xe=N.value)==null?void 0:xe.title)||"未知书籍"),1)]),e("div",Hn,[l(W,{onClick:se,disabled:$.value<=0,icon:_(Be),circle:"",size:"small",title:"上一章"},null,8,["disabled","icon"]),e("div",Nn,[e("span",On," 第 "+U($.value+1)+" 章 / 共 "+U(V.value)+" 章 ",1),e("span",Gn,U(Math.round(P.value))+"%",1)]),l(W,{onClick:fe,disabled:$.value>=V.value-1,icon:_(Lt),circle:"",size:"small",title:"下一章"},null,8,["disabled","icon"])]),e("div",Wn,[l(W,{onClick:g[0]||(g[0]=E=>y.value=!0),icon:_(Vt),circle:"",title:"目录"},null,8,["icon"]),l(W,{onClick:g[1]||(g[1]=E=>B.value=!0),icon:_(Pe),circle:"",title:"搜索"},null,8,["icon"]),l(W,{onClick:g[2]||(g[2]=E=>G.value=!0),icon:_(tt),circle:"",title:"设置"},null,8,["icon"])])]),e("div",jn,[e("div",Kn,[e("h2",Yn,U((Te=O.value)==null?void 0:Te.title),1),e("div",{class:"chapter-content",style:lt(Y.value),innerHTML:(Ae=O.value)==null?void 0:Ae.content,onClick:Q},null,12,Zn)])],512)])):K("",!0),l(ee,{modelValue:y.value,"onUpdate:modelValue":g[3]||(g[3]=E=>y.value=E),title:"目录",direction:"ltr",size:"400px"},{default:C(()=>[e("div",Qn,[(m(!0),k(Ce,null,$e(M.value,(E,_e)=>(m(),k("div",{key:E.id,class:ye(["toc-item",{active:(E.chapterIndex??_e)===$.value}]),onClick:Ne=>ce(E.chapterIndex??_e)},[e("span",Jn,U(E.title),1),e("span",Xn,U(E.level),1)],10,qn))),128))])]),_:1},8,["modelValue"]),l(ee,{modelValue:B.value,"onUpdate:modelValue":g[5]||(g[5]=E=>B.value=E),title:"搜索",direction:"rtl",size:"400px"},{default:C(()=>[e("div",el,[l(we,{modelValue:x.value,"onUpdate:modelValue":g[4]||(g[4]=E=>x.value=E),placeholder:"输入搜索关键词",onKeyup:ot(h,["enter"]),clearable:""},{append:C(()=>[l(W,{onClick:h,icon:_(Pe)},null,8,["icon"])]),_:1},8,["modelValue"]),D.value.length>0?(m(),k("div",tl,[e("p",ol,"找到 "+U(D.value.length)+" 个结果",1),(m(!0),k(Ce,null,$e(D.value,(E,_e)=>(m(),k("div",{key:_e,class:"search-result-item",onClick:Ne=>s(E)},[e("div",ll,U(E.context),1),e("div",al," 第"+U(E.chapterIndex+1)+"章 - "+U(E.chapterTitle),1)],8,nl))),128))])):x.value&&!q.value?(m(),k("div",sl,[l(pe,{description:"未找到匹配的内容"})])):K("",!0)])]),_:1},8,["modelValue"]),l(ee,{modelValue:G.value,"onUpdate:modelValue":g[10]||(g[10]=E=>G.value=E),title:"阅读设置",direction:"rtl",size:"350px"},{default:C(()=>[e("div",il,[e("div",rl,[g[15]||(g[15]=e("h4",null,"字体设置",-1)),e("div",cl,[g[13]||(g[13]=e("label",null,"字体大小",-1)),l(he,{modelValue:R.value.fontSize,"onUpdate:modelValue":g[6]||(g[6]=E=>R.value.fontSize=E),min:12,max:24,step:1,onChange:I},null,8,["modelValue"])]),e("div",ul,[g[14]||(g[14]=e("label",null,"行高",-1)),l(he,{modelValue:R.value.lineHeight,"onUpdate:modelValue":g[7]||(g[7]=E=>R.value.lineHeight=E),min:1.2,max:2,step:.1,onChange:I},null,8,["modelValue"])])]),e("div",dl,[g[19]||(g[19]=e("h4",null,"主题设置",-1)),l(He,{modelValue:R.value.theme,"onUpdate:modelValue":g[8]||(g[8]=E=>R.value.theme=E),onChange:I},{default:C(()=>[l(de,{value:"light"},{default:C(()=>g[16]||(g[16]=[J("浅色")])),_:1,__:[16]}),l(de,{value:"dark"},{default:C(()=>g[17]||(g[17]=[J("深色")])),_:1,__:[17]}),l(de,{value:"sepia"},{default:C(()=>g[18]||(g[18]=[J("护眼")])),_:1,__:[18]})]),_:1},8,["modelValue"])]),e("div",vl,[g[21]||(g[21]=e("h4",null,"布局设置",-1)),e("div",gl,[g[20]||(g[20]=e("label",null,"最大宽度",-1)),l(he,{modelValue:R.value.maxWidth,"onUpdate:modelValue":g[9]||(g[9]=E=>R.value.maxWidth=E),min:600,max:1200,step:50,onChange:I},null,8,["modelValue"])])])])]),_:1},8,["modelValue"])])}}}),pl=Me(fl,[["__scopeId","data-v-34a525ba"]]),hl={class:"unified-reader"},ml={key:0,class:"loading-container"},wl={key:1,class:"error-container"},_l={key:5,class:"format-placeholder"},kl={key:6,class:"unsupported-format"},Pl=Ve({__name:"UnifiedReader",props:{bookId:{}},setup(w){const t=w,n=je(),a=f(!0),d=f(""),r=f(null),u=f(""),i=f(""),c=f(1),b=oe(()=>["txt","pdf","epub","mobi"]),z=async()=>{try{a.value=!0,d.value="",console.log(`UnifiedReader: 加载书籍信息 ${t.bookId}`);const P=async(R=100,Y=100)=>{console.log("UnifiedReader: 开始检查Electron环境...");for(let L=0;L<R;L++){if(typeof window>"u"){console.log(`UnifiedReader: 等待window对象... (${L+1}/${R})`),await new Promise(F=>setTimeout(F,Y));continue}if(!window.electronAPI){console.log(`UnifiedReader: 等待electronAPI对象... (${L+1}/${R})`),await new Promise(F=>setTimeout(F,Y));continue}return console.log("UnifiedReader: Electron环境检查通过"),!0}return console.error("UnifiedReader: Electron环境检查超时"),!1},y=async(R=50,Y=200)=>{console.log("UnifiedReader: 开始检查API可用性...");for(let L=0;L<R;L++)try{if(!window.electronAPI){console.log(`UnifiedReader: electronAPI对象不存在 (${L+1}/${R})`),await new Promise(F=>setTimeout(F,Y));continue}if(!window.electronAPI.reader){console.log(`UnifiedReader: reader API不存在 (${L+1}/${R})`),await new Promise(F=>setTimeout(F,Y));continue}if(typeof window.electronAPI.reader.getBook!="function"){console.log(`UnifiedReader: getBook方法不是函数 (${L+1}/${R})`),await new Promise(F=>setTimeout(F,Y));continue}return console.log("UnifiedReader: API可用性检查通过"),!0}catch(F){console.warn(`UnifiedReader: API检查异常 (${L+1}/${R}):`,F),await new Promise(ie=>setTimeout(ie,Y))}return console.error("UnifiedReader: API可用性检查超时"),!1};if(!await P())throw new Error("Electron环境未就绪，请确保应用在Electron环境中运行");if(!await y())throw new Error("阅读器API初始化超时，请稍后重试或重启应用");console.log("UnifiedReader: 开始调用阅读器API获取书籍信息");let x=null,D=0;const q=3;for(;D<q&&!x;)try{if(console.log(`UnifiedReader: API调用尝试 ${D+1}/${q}`),x=await window.electronAPI.reader.getBook(t.bookId),x){console.log("UnifiedReader: 成功获取书籍信息");break}else console.warn(`UnifiedReader: API返回空结果 (尝试 ${D+1}/${q})`)}catch(R){if(D++,console.error(`UnifiedReader: API调用失败 (尝试 ${D}/${q}):`,R),D<q)console.log("UnifiedReader: 将在1秒后重试..."),await new Promise(Y=>setTimeout(Y,1e3));else throw new Error(`API调用失败: ${R.message||R}`)}if(!x)throw new Error("书籍不存在或无法获取书籍信息");if(!x.id||!x.title)throw new Error("书籍数据不完整，缺少必要字段");r.value=x,u.value=x.file_format||x.format||"txt",i.value=x.file_path||x.filePath||"",x.current_page&&(c.value=x.current_page),console.log("UnifiedReader: 书籍信息加载成功",{id:x.id,title:x.title,format:u.value,filePath:i.value,initialPage:c.value}),b.value.includes(u.value)||console.warn(`UnifiedReader: 不支持的格式 ${u.value}`)}catch(P){console.error("UnifiedReader: 加载书籍信息失败:",P);let y="加载失败";P.message?y=P.message:typeof P=="string"?y=P:y="未知错误，请查看控制台获取详细信息",d.value=y}finally{a.value=!1}},N=()=>{const P={timestamp:new Date().toISOString(),environment:{isElectron:typeof window<"u"&&!!window.electronAPI,hasWindow:typeof window<"u",userAgent:typeof navigator<"u"?navigator.userAgent:"unknown"},api:{electronAPI:!!window.electronAPI,readerAPI:!!(window.electronAPI&&window.electronAPI.reader),getBookMethod:!!(window.electronAPI&&window.electronAPI.reader&&typeof window.electronAPI.reader.getBook=="function")},bookId:t.bookId,currentState:{isLoading:a.value,hasError:!!d.value,errorMessage:d.value,hasBookInfo:!!r.value}};return console.log("UnifiedReader: 环境诊断报告",P),P},M=async()=>{console.log("UnifiedReader: 开始智能重试...");const P=N();if(!P.environment.isElectron){d.value="应用未在Electron环境中运行，请重启应用";return}if(!P.api.electronAPI){d.value="Electron API未初始化，请等待应用完全加载后重试";return}if(!P.api.readerAPI){d.value="阅读器API不可用，请检查应用状态或重启应用";return}d.value="",console.log("UnifiedReader: 延迟2秒后重试..."),await new Promise(y=>setTimeout(y,2e3)),await z()},O=()=>{M()},$=()=>{console.log("UnifiedReader: 返回图书列表"),n.push("/bookshelf/library")},V=async()=>{console.log("UnifiedReader: 执行环境预检查...");try{if(typeof window>"u")throw new Error("Window对象不可用");return await new Promise(P=>setTimeout(P,500)),window.electronAPI?(console.log("UnifiedReader: ElectronAPI已可用"),window.electronAPI.reader?console.log("UnifiedReader: 阅读器API已可用"):console.warn("UnifiedReader: 阅读器API暂未可用")):console.warn("UnifiedReader: ElectronAPI暂未可用，将在加载时等待"),console.log("UnifiedReader: 环境预检查完成"),!0}catch(P){return console.error("UnifiedReader: 环境预检查失败:",P),!1}};return Fe(async()=>{console.log("UnifiedReader: 组件已挂载，开始初始化..."),await V()||console.warn("UnifiedReader: 环境预检查未通过，但仍尝试加载书籍信息"),await z()}),We(()=>t.bookId,()=>{t.bookId&&z()}),(P,y)=>{var x;const B=Z("el-icon"),G=Z("el-button");return m(),k("div",hl,[a.value?(m(),k("div",ml,[l(B,{class:"is-loading"},{default:C(()=>[l(_(ze))]),_:1}),y[0]||(y[0]=e("span",null,"正在加载阅读器...",-1))])):d.value?(m(),k("div",wl,[l(B,null,{default:C(()=>[l(_(Le))]),_:1}),e("span",null,U(d.value),1),l(G,{onClick:O,type:"primary",size:"small"},{default:C(()=>y[1]||(y[1]=[J("重试")])),_:1,__:[1]})])):u.value==="txt"?(m(),Ue(an,{key:2,"book-id":P.bookId,onGoBack:$},null,8,["book-id"])):u.value==="pdf"?(m(),Ue(An,{key:3,"book-id":P.bookId,onGoBack:$},null,8,["book-id"])):u.value==="epub"?(m(),Ue(pl,{key:4,"book-id":P.bookId,onGoBack:$},null,8,["book-id"])):u.value==="mobi"?(m(),k("div",_l,[l(B,null,{default:C(()=>[l(_(nt))]),_:1}),y[3]||(y[3]=e("h3",null,"MOBI阅读器",-1)),y[4]||(y[4]=e("p",null,"MOBI格式支持正在开发中...",-1)),l(G,{onClick:$,type:"primary"},{default:C(()=>y[2]||(y[2]=[J("返回图书列表")])),_:1,__:[2]})])):(m(),k("div",kl,[l(B,null,{default:C(()=>[l(_(Le))]),_:1}),y[6]||(y[6]=e("h3",null,"不支持的文件格式",-1)),e("p",null,"当前不支持 "+U((x=u.value)==null?void 0:x.toUpperCase())+" 格式的文件",1),l(G,{onClick:$,type:"primary"},{default:C(()=>y[5]||(y[5]=[J("返回图书列表")])),_:1,__:[5]})]))])}}}),$l=Me(Pl,[["__scopeId","data-v-0ef1b30b"]]);export{$l as default};
