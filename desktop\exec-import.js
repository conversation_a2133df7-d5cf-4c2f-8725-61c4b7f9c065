
const { BrowserWindow } = require('electron');

async function executeImport() {
  const windows = BrowserWindow.getAllWindows();
  if (windows.length === 0) {
    console.error('❌ 没有找到 Electron 窗口');
    return;
  }

  const mainWindow = windows[0];
  console.log('✅ 找到主窗口，开始执行导入');

  const script = `
// 批量导入脚本 - 在 Electron 应用开发者工具中执行
console.log('🎯 开始批量导入 37 本图书...');

const filesToImport = [
  "D:\\reader\\desktop\\books\\epub\\中国古代建筑艺术.epub",
  "D:\\reader\\desktop\\books\\epub\\人工智能原理与应用.epub",
  "D:\\reader\\desktop\\books\\epub\\山海经新解.epub",
  "D:\\reader\\desktop\\books\\epub\\心理学与生活.epub",
  "D:\\reader\\desktop\\books\\epub\\心理学与生活_processed.epub",
  "D:\\reader\\desktop\\books\\epub\\数据结构与算法.epub",
  "D:\\reader\\desktop\\books\\epub\\春江花月夜.epub",
  "D:\\reader\\desktop\\books\\epub\\测试EPUB.epub",
  "D:\\reader\\desktop\\books\\epub\\测试EPUB_processed.epub",
  "D:\\reader\\desktop\\books\\epub\\现代JavaScript开发指南.epub",
  "D:\\reader\\desktop\\books\\epub\\现代诗歌选集.epub",
  "D:\\reader\\desktop\\books\\epub\\茶道文化.epub",
  "D:\\reader\\desktop\\books\\epub\\茶道文化_backup.epub",
  "D:\\reader\\desktop\\books\\epub\\茶道文化_正确内容.txt",
  "D:\\reader\\desktop\\books\\epub\\量子物理学导论.epub",
  "D:\\reader\\desktop\\books\\pdf\\test-document.pdf",
  "D:\\reader\\desktop\\books\\pdf\\《JavaScript高级编程》.pdf",
  "D:\\reader\\desktop\\books\\pdf\\《Python编程实战》.pdf",
  "D:\\reader\\desktop\\books\\pdf\\《世界文明史》.pdf",
  "D:\\reader\\desktop\\books\\pdf\\《中国通史》.pdf",
  "D:\\reader\\desktop\\books\\pdf\\《人工智能原理》.pdf",
  "D:\\reader\\desktop\\books\\pdf\\《哲学思辨录》.pdf",
  "D:\\reader\\desktop\\books\\pdf\\《生物学基础》.pdf",
  "D:\\reader\\desktop\\books\\pdf\\《红楼梦》.pdf",
  "D:\\reader\\desktop\\books\\pdf\\《诗经选读》.pdf",
  "D:\\reader\\desktop\\books\\pdf\\《量子物理学导论》.pdf",
  "D:\\reader\\desktop\\books\\txt\\1_人工智能简史.txt",
  "D:\\reader\\desktop\\books\\txt\\2_量子物理学导论.txt",
  "D:\\reader\\desktop\\books\\txt\\3_现代软件工程.txt",
  "D:\\reader\\desktop\\books\\txt\\4_数据科学实战.txt",
  "D:\\reader\\desktop\\books\\txt\\5_区块链技术原理.txt",
  "D:\\reader\\desktop\\books\\txt\\6_云计算架构设计.txt",
  "D:\\reader\\desktop\\books\\txt\\7_网络安全防护.txt",
  "D:\\reader\\desktop\\books\\txt\\8_移动应用开发.txt",
  "D:\\reader\\desktop\\books\\txt\\人工智能简史.txt",
  "D:\\reader\\desktop\\books\\txt\\数据科学实战.txt",
  "D:\\reader\\desktop\\books\\txt\\现代软件工程.txt"
];

async function batchImport() {
  let successCount = 0;
  let failCount = 0;
  
  // 检查导入前状态
  const beforeBooks = await window.electronAPI.book.list();
  console.log(\`导入前数据库中有 \${beforeBooks.length} 本图书\`);
  
  for (let i = 0; i < filesToImport.length; i++) {
    const filePath = filesToImport[i];
    const fileName = filePath.split(/[\\\/]/).pop();
    
    console.log(\`\n[\${i + 1}/\${filesToImport.length}] 正在导入: \${fileName}\`);
    console.log(\`  路径: \${filePath}\`);
    
    try {
      const result = await window.electronAPI.book.add(filePath);
      console.log(\`  ✅ 导入成功: \${result.title || fileName}\`);
      console.log(\`     ID: \${result.id}, 作者: \${result.author || '未知'}\`);
      console.log(\`     格式: \${result.file_format}, 大小: \${result.file_size} 字节\`);
      successCount++;
      
      // 短暂延迟
      await new Promise(resolve => setTimeout(resolve, 100));
      
    } catch (error) {
      console.error(\`  ❌ 导入失败: \${error.message}\`);
      failCount++;
    }
  }
  
  // 检查导入后状态
  const afterBooks = await window.electronAPI.book.list();
  console.log(\`\n📊 导入完成统计:\`);
  console.log(\`  总计: \${filesToImport.length} 本\`);
  console.log(\`  成功: \${successCount} 本\`);
  console.log(\`  失败: \${failCount} 本\`);
  console.log(\`  导入前: \${beforeBooks.length} 本\`);
  console.log(\`  导入后: \${afterBooks.length} 本\`);
  console.log(\`  实际新增: \${afterBooks.length - beforeBooks.length} 本\`);
  
  // 显示新导入的图书
  if (afterBooks.length > beforeBooks.length) {
    console.log(\`\n📚 新导入的图书:\`);
    const newBooks = afterBooks.slice(beforeBooks.length);
    newBooks.forEach((book, index) => {
      console.log(\`  \${index + 1}. 《\${book.title}》 - \${book.author || '未知作者'}\`);
      console.log(\`     格式: \${book.file_format} | 大小: \${book.file_size} 字节\`);
    });
  }
  
  console.log(\`\n🎉 批量导入完成!\`);
  return { total: filesToImport.length, success: successCount, failed: failCount };
}

// 执行导入
batchImport().then(result => {
  console.log('✅ 导入任务完成!', result);
}).catch(error => {
  console.error('💥 导入任务失败:', error);
});`;
  
  try {
    const result = await mainWindow.webContents.executeJavaScript(script);
    console.log('✅ 导入脚本执行完成:', result);
    return result;
  } catch (error) {
    console.error('❌ 执行导入脚本失败:', error);
    throw error;
  }
}

executeImport().then(result => {
  console.log('🎉 批量导入完成!', result);
}).catch(error => {
  console.error('💥 批量导入失败:', error);
});
