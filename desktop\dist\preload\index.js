"use strict";const r=require("electron"),o={book:{add:e=>r.ipcRenderer.invoke("book:add",e),remove:e=>r.ipcRenderer.invoke("book:remove",e),list:()=>r.ipcRenderer.invoke("book:list"),get:e=>r.ipcRenderer.invoke("book:get",e),updateProgress:(e,i,n)=>r.ipcRenderer.invoke("book:update-progress",e,i,n),getCover:e=>r.ipcRenderer.invoke("book:get-cover",e)},bookmark:{add:e=>r.ipcRenderer.invoke("bookmark:add",e),remove:e=>r.ipcRenderer.invoke("bookmark:remove",e),list:e=>r.ipcRenderer.invoke("bookmark:list",e),update:(e,i)=>r.ipcRenderer.invoke("bookmark:update",e,i)},note:{add:e=>r.ipcRenderer.invoke("note:add",e),remove:e=>r.ipcRenderer.invoke("note:remove",e),list:e=>r.ipcRenderer.invoke("note:list",e),update:(e,i)=>r.ipcRenderer.invoke("note:update",e,i)},settings:{get:()=>r.ipcRenderer.invoke("settings:get"),update:e=>r.ipcRenderer.invoke("settings:update",e)},file:{read:e=>r.ipcRenderer.invoke("file:read",e),exists:e=>r.ipcRenderer.invoke("file:exists",e),select:e=>r.ipcRenderer.invoke("file:select",e)},txtReader:{detectEncoding:e=>r.ipcRenderer.invoke("txt-reader:detect-encoding",e),getFileInfo:e=>r.ipcRenderer.invoke("txt-reader:get-file-info",e),readFile:(e,i)=>r.ipcRenderer.invoke("txt-reader:read-file",e,i),createReader:(e,i,n)=>r.ipcRenderer.invoke("txt-reader:create",e,i,n),destroyReader:e=>r.ipcRenderer.invoke("txt-reader:destroy",e),getPageContent:(e,i)=>r.ipcRenderer.invoke("txt-reader:get-page-content",e,i),search:(e,i,n)=>r.ipcRenderer.invoke("txt-reader:search",e,i,n)},epubReader:{parseEpub:e=>r.ipcRenderer.invoke("epub-reader:parse-epub",e),getFileInfo:e=>r.ipcRenderer.invoke("epub-reader:get-file-info",e),createReader:(e,i,n)=>r.ipcRenderer.invoke("epub-reader:create",e,i,n),destroyReader:e=>r.ipcRenderer.invoke("epub-reader:destroy",e),getChapter:(e,i)=>r.ipcRenderer.invoke("epub-reader:get-chapter",e,i),getToc:e=>r.ipcRenderer.invoke("epub-reader:get-toc",e),search:(e,i)=>r.ipcRenderer.invoke("epub-reader:search",e,i),navigate:(e,i,n)=>r.ipcRenderer.invoke("epub-reader:navigate",e,i,n),getStatus:e=>r.ipcRenderer.invoke("epub-reader:get-status",e),updatePosition:(e,i)=>r.ipcRenderer.invoke("epub-reader:update-position",e,i),getCreationProgress:e=>r.ipcRenderer.invoke("epub-reader:get-creation-progress",e)},invoke:(e,...i)=>r.ipcRenderer.invoke(e,...i),window:{minimize:()=>r.ipcRenderer.invoke("window:minimize"),maximize:()=>r.ipcRenderer.invoke("window:maximize"),close:()=>r.ipcRenderer.invoke("window:close"),toggleFullscreen:()=>r.ipcRenderer.invoke("window:toggle-fullscreen")},theme:{set:e=>r.ipcRenderer.invoke("theme:set",e),get:()=>r.ipcRenderer.invoke("theme:get"),list:()=>r.ipcRenderer.invoke("theme:list"),onChange:e=>{r.ipcRenderer.on("theme:change",(i,n)=>e(n))},removeChangeListener:()=>{r.ipcRenderer.removeAllListeners("theme:change")}},reader:{getBook:e=>r.ipcRenderer.invoke("reader:get-book",e),getProgress:e=>r.ipcRenderer.invoke("reader:get-progress",e),updateProgress:(e,i,n)=>r.ipcRenderer.invoke("reader:update-progress",e,i,n),checkFile:e=>r.ipcRenderer.invoke("reader:check-file",e),getBooks:()=>r.ipcRenderer.invoke("reader:get-books")},system:{platform:process.platform,version:process.versions.electron}};r.contextBridge.exposeInMainWorld("electronAPI",o);
