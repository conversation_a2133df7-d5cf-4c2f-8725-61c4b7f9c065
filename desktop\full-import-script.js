
console.log('🎯 开始系统批量导入 37 本图书...');

const filesToImport = [
  "D:\\reader\\desktop\\books\\epub\\中国古代建筑艺术.epub",
  "D:\\reader\\desktop\\books\\epub\\人工智能原理与应用.epub",
  "D:\\reader\\desktop\\books\\epub\\山海经新解.epub",
  "D:\\reader\\desktop\\books\\epub\\心理学与生活.epub",
  "D:\\reader\\desktop\\books\\epub\\心理学与生活_processed.epub",
  "D:\\reader\\desktop\\books\\epub\\数据结构与算法.epub",
  "D:\\reader\\desktop\\books\\epub\\春江花月夜.epub",
  "D:\\reader\\desktop\\books\\epub\\测试EPUB.epub",
  "D:\\reader\\desktop\\books\\epub\\测试EPUB_processed.epub",
  "D:\\reader\\desktop\\books\\epub\\现代JavaScript开发指南.epub",
  "D:\\reader\\desktop\\books\\epub\\现代诗歌选集.epub",
  "D:\\reader\\desktop\\books\\epub\\茶道文化.epub",
  "D:\\reader\\desktop\\books\\epub\\茶道文化_backup.epub",
  "D:\\reader\\desktop\\books\\epub\\茶道文化_正确内容.txt",
  "D:\\reader\\desktop\\books\\epub\\量子物理学导论.epub",
  "D:\\reader\\desktop\\books\\pdf\\test-document.pdf",
  "D:\\reader\\desktop\\books\\pdf\\《JavaScript高级编程》.pdf",
  "D:\\reader\\desktop\\books\\pdf\\《Python编程实战》.pdf",
  "D:\\reader\\desktop\\books\\pdf\\《世界文明史》.pdf",
  "D:\\reader\\desktop\\books\\pdf\\《中国通史》.pdf",
  "D:\\reader\\desktop\\books\\pdf\\《人工智能原理》.pdf",
  "D:\\reader\\desktop\\books\\pdf\\《哲学思辨录》.pdf",
  "D:\\reader\\desktop\\books\\pdf\\《生物学基础》.pdf",
  "D:\\reader\\desktop\\books\\pdf\\《红楼梦》.pdf",
  "D:\\reader\\desktop\\books\\pdf\\《诗经选读》.pdf",
  "D:\\reader\\desktop\\books\\pdf\\《量子物理学导论》.pdf",
  "D:\\reader\\desktop\\books\\txt\\1_人工智能简史.txt",
  "D:\\reader\\desktop\\books\\txt\\2_量子物理学导论.txt",
  "D:\\reader\\desktop\\books\\txt\\3_现代软件工程.txt",
  "D:\\reader\\desktop\\books\\txt\\4_数据科学实战.txt",
  "D:\\reader\\desktop\\books\\txt\\5_区块链技术原理.txt",
  "D:\\reader\\desktop\\books\\txt\\6_云计算架构设计.txt",
  "D:\\reader\\desktop\\books\\txt\\7_网络安全防护.txt",
  "D:\\reader\\desktop\\books\\txt\\8_移动应用开发.txt",
  "D:\\reader\\desktop\\books\\txt\\人工智能简史.txt",
  "D:\\reader\\desktop\\books\\txt\\数据科学实战.txt",
  "D:\\reader\\desktop\\books\\txt\\现代软件工程.txt"
];

async function systemBatchImport() {
  console.log('📊 导入统计:');
  console.log('  EPUB: 14 本');
  console.log('  TXT: 12 本');
  console.log('  PDF: 11 本');
  console.log('');
  
  let successCount = 0;
  let failCount = 0;
  const results = [];
  const startTime = Date.now();
  
  // 检查导入前状态
  console.log('🔍 检查导入前数据库状态...');
  const beforeBooks = await window.electronAPI.book.list();
  console.log(`导入前数据库中有 ${beforeBooks.length} 本图书`);
  
  console.log('\n🚀 开始批量导入过程...');
  
  for (let i = 0; i < filesToImport.length; i++) {
    const filePath = filesToImport[i];
    const fileName = filePath.split(/[\\\/]/).pop();
    const fileFormat = fileName.split('.').pop().toUpperCase();
    
    console.log(`\n[${i + 1}/${filesToImport.length}] 正在导入: ${fileName}`);
    console.log(`  📁 路径: ${filePath}`);
    console.log(`  📄 格式: ${fileFormat}`);
    
    try {
      // 检查文件是否存在
      const fileExists = await window.electronAPI.file.exists(filePath);
      if (!fileExists) {
        throw new Error('文件不存在');
      }
      
      // 调用导入API
      const result = await window.electronAPI.book.add(filePath);
      
      console.log(`  ✅ 导入成功: 《${result.title || fileName}》`);
      console.log(`     📖 ID: ${result.id}`);
      console.log(`     👤 作者: ${result.author || '未知作者'}`);
      console.log(`     📊 格式: ${result.file_format}`);
      console.log(`     💾 大小: ${result.file_size} 字节 (${(result.file_size / 1024).toFixed(1)} KB)`);
      console.log(`     📅 导入时间: ${result.import_time}`);
      
      results.push({
        index: i + 1,
        file: fileName,
        success: true,
        result: result,
        message: '导入成功',
        title: result.title,
        author: result.author,
        format: result.file_format,
        size: result.file_size
      });
      
      successCount++;
      
      // 显示进度
      const progress = ((i + 1) / filesToImport.length * 100).toFixed(1);
      console.log(`  📈 进度: ${progress}% (${i + 1}/${filesToImport.length})`);
      
      // 短暂延迟避免过快导入
      await new Promise(resolve => setTimeout(resolve, 150));
      
    } catch (error) {
      console.error(`  ❌ 导入失败: ${error.message}`);
      
      results.push({
        index: i + 1,
        file: fileName,
        success: false,
        error: error.message,
        message: `导入失败: ${error.message}`
      });
      
      failCount++;
    }
  }
  
  const endTime = Date.now();
  const duration = ((endTime - startTime) / 1000).toFixed(1);
  
  // 检查导入后状态
  console.log('\n🔍 检查导入后数据库状态...');
  const afterBooks = await window.electronAPI.book.list();
  
  console.log(`\n📊 批量导入完成统计:`);
  console.log(`  ⏱️  总耗时: ${duration} 秒`);
  console.log(`  📚 总计: ${filesToImport.length} 本`);
  console.log(`  ✅ 成功: ${successCount} 本`);
  console.log(`  ❌ 失败: ${failCount} 本`);
  console.log(`  📈 成功率: ${(successCount / filesToImport.length * 100).toFixed(1)}%`);
  console.log(`  🗄️  导入前: ${beforeBooks.length} 本`);
  console.log(`  🗄️  导入后: ${afterBooks.length} 本`);
  console.log(`  ➕ 实际新增: ${afterBooks.length - beforeBooks.length} 本`);
  
  // 显示成功导入的图书
  const successResults = results.filter(r => r.success);
  if (successResults.length > 0) {
    console.log(`\n📚 成功导入的图书列表:`);
    successResults.forEach((result, index) => {
      console.log(`  ${index + 1}. 《${result.title || result.file}》 - ${result.author || '未知作者'}`);
      console.log(`     格式: ${result.format} | 大小: ${(result.size / 1024).toFixed(1)} KB`);
    });
  }
  
  // 显示失败的文件
  const failedResults = results.filter(r => !r.success);
  if (failedResults.length > 0) {
    console.log(`\n❌ 导入失败的文件:`);
    failedResults.forEach((result, index) => {
      console.log(`  ${index + 1}. ${result.file}: ${result.message}`);
    });
  }
  
  console.log(`\n🎉 系统批量导入任务完成!`);
  console.log(`📋 详细结果已记录在控制台中`);
  
  return {
    total: filesToImport.length,
    success: successCount,
    failed: failCount,
    duration: duration,
    beforeCount: beforeBooks.length,
    afterCount: afterBooks.length,
    results: results
  };
}

// 立即执行导入
systemBatchImport().then(result => {
  console.log('\n✅ 系统批量导入任务完成!', result);
  
  // 可选：刷新页面以显示新导入的图书
  if (result.success > 0) {
    console.log('\n🔄 建议刷新页面以查看新导入的图书');
  }
  
}).catch(error => {
  console.error('\n💥 系统批量导入任务失败:', error);
});
