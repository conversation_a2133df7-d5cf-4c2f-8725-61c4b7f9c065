import{N as I,b as g,R as U,e as u,C as m}from"./index-BqsStGhU.js";const H=I("unifiedLibrary",()=>{const o=g([]),S=g(!1),p=g(null),A=g(""),l=U({format:"all",readingStatus:"all",author:"all",publisher:"all",language:"all",tags:[]}),h=g("addedAt"),f=g("desc"),P=g("grid"),w=g(20),i=g(1),L=u(()=>{let t=o.value;if(A.value.trim()){const e=A.value.toLowerCase().trim();t=t.filter(r=>r.title.toLowerCase().includes(e)||r.author&&r.author.toLowerCase().includes(e)||r.description&&r.description.toLowerCase().includes(e)||r.publisher&&r.publisher.toLowerCase().includes(e)||r.tags.some(s=>s.toLowerCase().includes(e)))}return l.format!=="all"&&(t=t.filter(e=>e.format===l.format)),l.readingStatus!=="all"&&(t=t.filter(e=>e.readingStatus===l.readingStatus)),l.author!=="all"&&(t=t.filter(e=>e.author===l.author)),l.publisher!=="all"&&(t=t.filter(e=>e.publisher===l.publisher)),l.language!=="all"&&(t=t.filter(e=>e.language===l.language)),l.tags.length>0&&(t=t.filter(e=>l.tags.some(r=>e.tags.includes(r)))),t}),y=u(()=>{const t=[...L.value];return t.sort((e,r)=>{var c,d;let s,a;switch(h.value){case"title":s=e.title.toLowerCase(),a=r.title.toLowerCase();break;case"author":s=(e.author||"").toLowerCase(),a=(r.author||"").toLowerCase();break;case"addedAt":s=e.addedAt.getTime(),a=r.addedAt.getTime();break;case"lastReadAt":s=((c=e.lastReadAt)==null?void 0:c.getTime())||0,a=((d=r.lastReadAt)==null?void 0:d.getTime())||0;break;case"readProgress":s=e.readProgress,a=r.readProgress;break;case"fileSize":s=e.fileSize,a=r.fileSize;break;default:return 0}return s<a?f.value==="asc"?-1:1:s>a?f.value==="asc"?1:-1:0}),t}),B=u(()=>{const t=(i.value-1)*w.value,e=t+w.value;return y.value.slice(t,e)}),E=u(()=>({currentPage:i.value,pageSize:w.value,total:y.value.length,totalPages:Math.ceil(y.value.length/w.value)})),D=u(()=>{const t=o.value.length,e=o.value.filter(n=>n.readingStatus==="unread").length,r=o.value.filter(n=>n.readingStatus==="reading").length,s=o.value.filter(n=>n.readingStatus==="finished").length,a=o.value.reduce((n,v)=>n+v.fileSize,0),c=o.value.reduce((n,v)=>n+v.totalPages,0),d=o.value.reduce((n,v)=>n+v.wordCount,0);return{total:t,unread:e,reading:r,finished:s,totalSize:a,totalPages:c,totalWords:d}}),b=u(()=>{const t=new Set(o.value.map(e=>e.format));return Array.from(t).sort()}),R=u(()=>{const t=new Set(o.value.map(e=>e.author).filter(Boolean));return Array.from(t).sort()}),C=u(()=>{const t=new Set(o.value.map(e=>e.publisher).filter(Boolean));return Array.from(t).sort()}),k=u(()=>{const t=new Set(o.value.map(e=>e.language));return Array.from(t).sort()}),z=u(()=>{const t=new Set;return o.value.forEach(e=>{e.tags.forEach(r=>t.add(r))}),Array.from(t).sort()});return{books:o,loading:S,error:p,searchQuery:A,filter:l,sortField:h,sortOrder:f,viewMode:P,pageSize:w,currentPage:i,filteredBooks:L,sortedBooks:y,paginatedBooks:B,pagination:E,stats:D,availableFormats:b,availableAuthors: <AUTHORS>
