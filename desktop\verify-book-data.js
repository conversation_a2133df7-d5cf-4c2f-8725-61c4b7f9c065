/**
 * 验证图书数据显示修复效果的测试脚本
 * 在浏览器开发者工具中运行此脚本来验证图书数据显示
 */

console.log('🔍 验证图书数据显示修复效果...')

async function verifyBookDataFix() {
  try {
    console.log('📋 检查项目:')
    console.log('1. 检查API调用是否正常')
    console.log('2. 检查UnifiedLibraryView是否正确显示数据库数据')
    console.log('3. 检查应用是否使用真实数据')
    console.log('')

    // 1. 测试API调用
    console.log('🧪 测试API调用...')
    if (!window.electronAPI || !window.electronAPI.book) {
      console.error('❌ electronAPI不可用')
      return
    }

    const books = await window.electronAPI.book.list()
    console.log(`✅ API调用成功，获取到 ${books.length} 本图书`)
    
    if (books.length > 0) {
      console.log('📚 数据库中的图书:')
      books.forEach((book, index) => {
        console.log(`  ${index + 1}. ${book.title} - ${book.author} (${book.file_format})`)
      })
    }

    // 2. 检查Vue应用状态
    console.log('\n🎯 检查Vue应用状态...')
    const app = document.querySelector('#app').__vue_app__
    if (!app) {
      console.error('❌ Vue应用实例不存在')
      return
    }

    const stores = app.config.globalProperties.$pinia._s
    console.log('📦 可用的Pinia stores:', Array.from(stores.keys()))

    // 3. 检查UnifiedLibrary store
    if (stores.has('unifiedLibrary')) {
      const store = stores.get('unifiedLibrary')
      console.log('\n📚 UnifiedLibrary store状态:')
      console.log(`  - books数量: ${store.books.length}`)
      console.log(`  - loading: ${store.loading}`)
      console.log(`  - error: ${store.error}`)
      
      if (store.books.length > 0) {
        console.log('  - 前端store中的图书:')
        store.books.slice(0, 3).forEach((book, index) => {
          console.log(`    ${index + 1}. ${book.title} - ${book.author}`)
        })
      }
    }

    // 4. 检查当前页面
    console.log('\n🌐 检查当前页面...')
    const currentPath = window.location.hash
    console.log(`当前路径: ${currentPath}`)

    // 5. 检查页面内容
    const bookCards = document.querySelectorAll('.book-card')
    console.log(`页面中的图书卡片数量: ${bookCards.length}`)

    if (bookCards.length > 0) {
      console.log('📖 页面显示的图书:')
      bookCards.forEach((card, index) => {
        const title = card.querySelector('h3')?.textContent || '未知标题'
        const author = card.querySelector('p')?.textContent || '未知作者'
        console.log(`  ${index + 1}. ${title} - ${author}`)
      })
    }

    // 6. 检查是否还有测试数据
    const testDataIndicators = [
      '示例图书',
      '三体',
      '活着',
      '百年孤独',
      '1984',
      '红楼梦'
    ]

    let hasTestData = false
    testDataIndicators.forEach(indicator => {
      if (document.body.textContent.includes(indicator)) {
        console.log(`⚠️  发现可能的测试数据: ${indicator}`)
        hasTestData = true
      }
    })

    if (!hasTestData) {
      console.log('✅ 未发现明显的测试数据')
    }

    console.log('\n🎉 验证完成!')
    
  } catch (error) {
    console.error('❌ 验证过程中出错:', error)
  }
}

// 立即执行验证
verifyBookDataFix()

// 导出到全局作用域
window.verifyBookDataFix = verifyBookDataFix
