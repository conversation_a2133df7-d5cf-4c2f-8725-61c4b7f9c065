/**
 * 数据库管理器
 * 负责数据库连接、初始化和查询操作
 */

import { app } from 'electron'
import { join, resolve } from 'path'
import { existsSync, mkdirSync, copyFileSync } from 'fs'
import Database from 'better-sqlite3'
import { v4 as uuidv4 } from 'uuid'

export class DatabaseManager {
  private db: Database.Database | null = null
  private dbPath: string
  private oldDbPath: string

  constructor() {
    // 获取数据库文件路径，优先使用环境变量，然后使用项目相对路径
    this.dbPath = this.getDatabasePath()
    // 延迟获取旧数据库路径，等到app ready后再获取
    this.oldDbPath = ''
    console.log('数据库路径:', this.dbPath)
  }

  /**
   * 获取数据库路径
   * 优先级：环境变量 DB_PATH > 项目相对路径 > 用户数据目录
   */
  private getDatabasePath(): string {
    // 1. 检查环境变量
    if (process.env.DB_PATH) {
      const envPath = resolve(process.env.DB_PATH)
      console.log('使用环境变量指定的数据库路径:', envPath)
      return envPath
    }

    // 2. 使用项目相对路径（新的默认路径）
    const projectPath = resolve(process.cwd(), 'database', 'yu-reader.db')
    console.log('使用项目相对路径:', projectPath)
    return projectPath
  }

  /**
   * 查找旧数据库文件路径
   * 检查两种可能的文件名：yureader.db 和 yu-reader.db
   */
  private findOldDatabasePath(): string {
    const userDataPath = app.getPath('userData')
    const possiblePaths = [
      join(userDataPath, 'yureader.db'),      // 实际发现的文件名
      join(userDataPath, 'yu-reader.db')      // 预期的文件名
    ]

    // 返回存在且较大的文件，或者第一个存在的文件
    let bestPath = possiblePaths[0] // 默认返回第一个
    let maxSize = 0

    for (const path of possiblePaths) {
      if (existsSync(path)) {
        try {
          const stats = require('fs').statSync(path)
          if (stats.size > maxSize) {
            maxSize = stats.size
            bestPath = path
          }
        } catch (error) {
          console.warn('检查文件大小失败:', path, error.message)
        }
      }
    }

    return bestPath
  }

  /**
   * 确保数据库目录存在
   */
  private ensureDatabaseDirectory(): void {
    const dbDir = join(this.dbPath, '..')
    if (!existsSync(dbDir)) {
      mkdirSync(dbDir, { recursive: true })
      console.log('创建数据库目录:', dbDir)
    }
  }

  /**
   * 迁移旧数据库文件到新位置
   */
  private migrateOldDatabase(): void {
    // 如果新位置已有数据库文件，跳过迁移
    if (existsSync(this.dbPath)) {
      console.log('新位置已存在数据库文件，跳过迁移')
      return
    }

    // 如果旧位置有数据库文件，进行迁移
    if (existsSync(this.oldDbPath)) {
      try {
        console.log('检测到旧数据库文件，开始迁移...')
        console.log('从:', this.oldDbPath)
        console.log('到:', this.dbPath)

        // 确保目标目录存在
        this.ensureDatabaseDirectory()

        // 复制数据库文件
        copyFileSync(this.oldDbPath, this.dbPath)
        console.log('数据库迁移完成')
      } catch (error) {
        console.error('数据库迁移失败:', error)
        console.log('将使用新位置创建新数据库')
      }
    } else {
      console.log('未检测到旧数据库文件，将在新位置创建数据库')
    }
  }

  /**
   * 初始化数据库
   */
  async initialize(): Promise<void> {
    try {
      // 现在app已经ready，可以安全获取旧数据库路径
      this.oldDbPath = this.findOldDatabasePath()
      console.log('旧数据库路径:', this.oldDbPath)

      // 确保数据库目录存在
      this.ensureDatabaseDirectory()

      // 迁移旧数据库文件（如果需要）
      this.migrateOldDatabase()

      // 创建数据库连接
      this.db = new Database(this.dbPath, {
        verbose: process.env.NODE_ENV === 'development' ? console.log : undefined
      })
      console.log('数据库连接已建立')

      // 创建必要的表
      await this.createTables()
      console.log('数据库表创建完成')

      // 导入测试数据（仅在开发环境且数据库为空时）
      // await this.importTestDataIfNeeded() // 暂时禁用测试数据导入

      return Promise.resolve()
    } catch (error) {
      console.error('数据库初始化失败:', error)
      return Promise.reject(error)
    }
  }

  /**
   * 创建数据库表
   */
  private async createTables(): Promise<void> {
    if (!this.db) {
      throw new Error('数据库未初始化')
    }

    // 检查并迁移books表结构
    await this.migrateBooksTable()

    // 创建其他表
    await this.createOtherTables()
  }

  /**
   * 迁移books表结构
   */
  private async migrateBooksTable(): Promise<void> {
    if (!this.db) {
      throw new Error('数据库未初始化')
    }

    try {
      // 检查books表是否存在
      const tableExists = this.db.prepare(`
        SELECT name FROM sqlite_master WHERE type='table' AND name='books'
      `).get()

      if (tableExists) {
        // 检查表结构是否需要迁移
        const columns = this.db.prepare(`PRAGMA table_info(books)`).all()
        const columnNames = columns.map((col: any) => col.name)

        // 检查是否有新的表结构字段
        const requiredColumns = [
          'id', 'title', 'author', 'isbn', 'file_path', 'file_format',
          'file_size', 'cover_image', 'description', 'publisher',
          'publish_date', 'language', 'total_pages', 'word_count',
          'reading_status', 'current_page', 'reading_progress_percent',
          'last_read_time', 'import_time', 'tags', 'metadata_json',
          'created_at', 'updated_at', 'deleted_at', 'sync_version'
        ]

        const missingColumns = requiredColumns.filter(col => !columnNames.includes(col))
        const hasOldStructure = columnNames.includes('format') && !columnNames.includes('file_format')

        if (missingColumns.length > 0 || hasOldStructure) {
          console.log('检测到旧的books表结构，开始迁移...')
          await this.migrateOldBooksTable()
        } else {
          console.log('books表结构已是最新版本')
        }
      } else {
        // 创建新的books表
        console.log('创建新的books表...')
        await this.createNewBooksTable()
      }
    } catch (error) {
      console.error('books表迁移失败:', error)
      throw error
    }
  }

  /**
   * 迁移旧的books表结构
   */
  private async migrateOldBooksTable(): Promise<void> {
    if (!this.db) {
      throw new Error('数据库未初始化')
    }

    console.log('开始迁移books表结构...')

    // 备份旧数据
    this.db.exec(`CREATE TABLE books_backup AS SELECT * FROM books`)

    // 删除旧表
    this.db.exec(`DROP TABLE books`)

    // 创建新表
    await this.createNewBooksTable()

    // 迁移数据
    try {
      const oldData = this.db.prepare(`SELECT * FROM books_backup`).all()

      if (oldData.length > 0) {
        console.log(`迁移 ${oldData.length} 条图书数据...`)

        const insertStmt = this.db.prepare(`
          INSERT INTO books (
            title, author, file_path, file_format, file_size, description,
            reading_status, current_page, reading_progress_percent,
            created_at, updated_at, deleted_at, sync_version
          ) VALUES (
            @title, @author, @file_path, @file_format, @file_size, @description,
            @reading_status, @current_page, @reading_progress_percent,
            @created_at, @updated_at, @deleted_at, @sync_version
          )
        `)

        for (const row of oldData) {
          const migratedRow = {
            title: row.title || '',
            author: row.author || '',
            file_path: row.file_path || '',
            file_format: row.format || row.file_format || '',
            file_size: row.file_size || 0,
            description: row.description || '',
            reading_status: row.status || row.reading_status || 'unread',
            current_page: row.current_page || 0,
            reading_progress_percent: row.progress || row.reading_progress_percent || 0,
            created_at: row.created_at || new Date().toISOString(),
            updated_at: row.updated_at || new Date().toISOString(),
            deleted_at: row.deleted_at || null,
            sync_version: row.sync_version || 1
          }

          insertStmt.run(migratedRow)
        }

        console.log('数据迁移完成')
      }
    } catch (error) {
      console.error('数据迁移失败:', error)
    }

    // 删除备份表
    this.db.exec(`DROP TABLE books_backup`)
    console.log('books表迁移完成')
  }

  /**
   * 创建新的books表
   */
  private async createNewBooksTable(): Promise<void> {
    if (!this.db) {
      throw new Error('数据库未初始化')
    }

    // 创建新的标准化图书表结构（按用户要求的精确规范）
    this.db.exec(`
      CREATE TABLE books (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT,
        author TEXT,
        isbn TEXT,
        file_path TEXT,
        file_format TEXT,
        file_size INTEGER,
        cover_image TEXT,
        description TEXT,
        publisher TEXT,
        publish_date TEXT,
        language TEXT,
        total_pages INTEGER,
        word_count INTEGER,
        reading_status TEXT,
        current_page INTEGER,
        reading_progress_percent REAL,
        last_read_time DATETIME,
        import_time DATETIME,
        tags TEXT,
        metadata_json TEXT,
        created_at DATETIME,
        updated_at DATETIME,
        deleted_at DATETIME,
        sync_version INTEGER
      )
    `)

    // 创建索引
    this.db.exec(`
      CREATE INDEX IF NOT EXISTS idx_books_title ON books (title);
      CREATE INDEX IF NOT EXISTS idx_books_author ON books (author);
      CREATE INDEX IF NOT EXISTS idx_books_isbn ON books (isbn);
      CREATE UNIQUE INDEX IF NOT EXISTS idx_books_file_path ON books (file_path);
      CREATE INDEX IF NOT EXISTS idx_books_file_format ON books (file_format);
      CREATE INDEX IF NOT EXISTS idx_books_reading_status ON books (reading_status);
      CREATE INDEX IF NOT EXISTS idx_books_language ON books (language);
      CREATE INDEX IF NOT EXISTS idx_books_publisher ON books (publisher);
      CREATE INDEX IF NOT EXISTS idx_books_import_time ON books (import_time);
      CREATE INDEX IF NOT EXISTS idx_books_last_read_time ON books (last_read_time);
      CREATE INDEX IF NOT EXISTS idx_books_created_at ON books (created_at);
      CREATE INDEX IF NOT EXISTS idx_books_updated_at ON books (updated_at);
      CREATE INDEX IF NOT EXISTS idx_books_deleted_at ON books (deleted_at);
      CREATE INDEX IF NOT EXISTS idx_books_sync_version ON books (sync_version);
    `)
  }

  /**
   * 创建其他表
   */
  private async createOtherTables(): Promise<void> {
    if (!this.db) {
      throw new Error('数据库未初始化')
    }

    // 创建用户表
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS users (
        id TEXT PRIMARY KEY,
        username TEXT UNIQUE NOT NULL,
        password_hash TEXT NOT NULL,
        email TEXT,
        avatar TEXT,
        created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // 创建书签表
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS bookmarks (
        id TEXT PRIMARY KEY,
        book_id TEXT NOT NULL,
        chapter_id TEXT,
        type TEXT NOT NULL DEFAULT 'bookmark',
        title TEXT NOT NULL,
        content TEXT,
        position TEXT NOT NULL,
        color TEXT,
        created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        deleted_at TEXT,
        sync_version INTEGER NOT NULL DEFAULT 1,
        FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE
      )
    `)

    // 创建笔记表
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS notes (
        id TEXT PRIMARY KEY,
        book_id TEXT NOT NULL,
        chapter_id TEXT,
        title TEXT NOT NULL,
        content TEXT NOT NULL,
        position TEXT,
        created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        deleted_at TEXT,
        sync_version INTEGER NOT NULL DEFAULT 1,
        FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE
      )
    `)

    // 创建学习任务表
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS learning_tasks (
        id TEXT PRIMARY KEY,
        type TEXT NOT NULL,
        title TEXT NOT NULL,
        description TEXT,
        book_id TEXT,
        status TEXT NOT NULL DEFAULT 'pending',
        due_date TEXT,
        completed_at TEXT,
        created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        deleted_at TEXT,
        sync_version INTEGER NOT NULL DEFAULT 1,
        FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE SET NULL
      )
    `)

    // 创建设置表
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS settings (
        key TEXT PRIMARY KEY,
        value TEXT NOT NULL,
        category TEXT NOT NULL,
        description TEXT,
        created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
      )
    `)
  }

  /**
   * 导入测试数据（仅在开发环境）
   */
  private async importTestDataIfNeeded(): Promise<void> {
    if (!this.db) {
      throw new Error('数据库未初始化')
    }

    try {
      // 检查是否已有数据
      const countStmt = this.db.prepare('SELECT COUNT(*) as count FROM books')
      const result = countStmt.get() as { count: number }

      if (result.count > 0) {
        console.log('数据库已有数据，跳过测试数据导入')
        return
      }

      console.log('正在导入测试数据...')

      // 测试数据 - 使用项目相对路径
      const projectRoot = process.cwd()
      const testBooksDir = resolve(projectRoot, 'desktop', 'test-books')

      const testBooks = [
        {
          title: '三体',
          author: '刘慈欣',
          isbn: '978-7-536-69293-0',
          file_path: resolve(testBooksDir, '三体.txt'),
          file_format: 'txt',
          file_size: 1024000,
          cover_image: null,
          description: '科幻小说经典之作，描述了人类文明与三体文明的第一次接触。',
          publisher: '重庆出版社',
          publish_date: '2006-05-01',
          language: 'zh-CN',
          total_pages: 180,
          word_count: 250000,
          reading_status: 'reading',
          current_page: 45,
          reading_progress_percent: 25.5,
          last_read_time: new Date().toISOString(),
          tags: JSON.stringify(['科幻', '小说', '刘慈欣', '三体']),
          metadata_json: JSON.stringify({
            difficulty: 'intermediate',
            category: 'science-fiction',
            series: 'three-body',
            rating: 4.9
          }),
          sync_version: 1
        },
        {
          title: '活着',
          author: '余华',
          isbn: '978-7-5063-4188-5',
          file_path: resolve(testBooksDir, '活着.txt'),
          file_format: 'txt',
          file_size: 512000,
          cover_image: null,
          description: '一个关于生存与坚韧的感人故事。',
          publisher: '作家出版社',
          publish_date: '1993-01-01',
          language: 'zh-CN',
          total_pages: 120,
          word_count: 180000,
          reading_status: 'finished',
          current_page: 120,
          reading_progress_percent: 100.0,
          last_read_time: new Date(Date.now() - 86400000).toISOString(), // 1天前
          tags: JSON.stringify(['文学', '小说', '余华', '现实主义']),
          metadata_json: JSON.stringify({
            difficulty: 'easy',
            category: 'literature',
            series: null,
            rating: 4.8
          }),
          sync_version: 1
        },
        {
          title: '百年孤独',
          author: '加西亚·马尔克斯',
          isbn: '978-7-5447-4095-2',
          file_path: resolve(testBooksDir, '百年孤独.txt'),
          file_format: 'txt',
          file_size: 768000,
          cover_image: null,
          description: '魔幻现实主义的代表作品。',
          publisher: '南海出版公司',
          publish_date: '2011-06-01',
          language: 'zh-CN',
          total_pages: 200,
          word_count: 300000,
          reading_status: 'unread',
          current_page: 0,
          reading_progress_percent: 0.0,
          last_read_time: null,
          tags: JSON.stringify(['文学', '小说', '魔幻现实主义', '马尔克斯']),
          metadata_json: JSON.stringify({
            difficulty: 'advanced',
            category: 'literature',
            series: null,
            rating: 4.9
          }),
          sync_version: 1
        },
        {
          title: '1984',
          author: '乔治·奥威尔',
          isbn: '978-7-5327-4657-5',
          file_path: resolve(testBooksDir, '1984.txt'),
          file_format: 'txt',
          file_size: 640000,
          cover_image: null,
          description: '反乌托邦小说的经典之作。',
          publisher: '上海译文出版社',
          publish_date: '2010-04-01',
          language: 'zh-CN',
          total_pages: 150,
          word_count: 220000,
          reading_status: 'reading',
          current_page: 90,
          reading_progress_percent: 60.0,
          last_read_time: new Date(Date.now() - 3600000).toISOString(), // 1小时前
          tags: JSON.stringify(['小说', '反乌托邦', '政治', '奥威尔']),
          metadata_json: JSON.stringify({
            difficulty: 'intermediate',
            category: 'fiction',
            series: null,
            rating: 4.7
          }),
          sync_version: 1
        },
        {
          title: '红楼梦',
          author: '曹雪芹',
          isbn: '978-7-02-008737-8',
          file_path: resolve(testBooksDir, '红楼梦.txt'),
          file_format: 'txt',
          file_size: 2048000,
          cover_image: null,
          description: '中国古典文学四大名著之一。',
          publisher: '人民文学出版社',
          publish_date: '2008-07-01',
          language: 'zh-CN',
          total_pages: 500,
          word_count: 800000,
          reading_status: 'reading',
          current_page: 80,
          reading_progress_percent: 15.0,
          last_read_time: new Date(Date.now() - 7200000).toISOString(), // 2小时前
          tags: JSON.stringify(['古典文学', '小说', '四大名著', '曹雪芹']),
          metadata_json: JSON.stringify({
            difficulty: 'advanced',
            category: 'classical-literature',
            series: 'four-great-novels',
            rating: 4.9
          }),
          sync_version: 1
        }
      ]

      // 检查表结构，确定正确的字段名
      const tableInfo = this.db.prepare("PRAGMA table_info(books)").all() as any[]
      const columnNames = tableInfo.map(col => col.name)

      console.log('books表字段:', columnNames)

      // 使用新的标准化INSERT语句
      const insertSQL = `
        INSERT INTO books (
          title, author, isbn, file_path, file_format, file_size,
          cover_image, description, publisher, publish_date, language,
          total_pages, word_count, reading_status, current_page,
          reading_progress_percent, last_read_time, tags, metadata_json,
          sync_version
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `

      const insertStmt = this.db.prepare(insertSQL)

      let successCount = 0
      for (const book of testBooks) {
        try {
          insertStmt.run(
            book.title, book.author, book.isbn, book.file_path,
            book.file_format, book.file_size, book.cover_image,
            book.description, book.publisher, book.publish_date,
            book.language, book.total_pages, book.word_count,
            book.reading_status, book.current_page,
            book.reading_progress_percent, book.last_read_time,
            book.tags, book.metadata_json, book.sync_version
          )
          successCount++
        } catch (error) {
          console.error(`插入测试数据失败: ${book.title}`, error)
        }
      }

      console.log(`测试数据导入完成，成功导入 ${successCount} 本图书`)

    } catch (error) {
      console.error('导入测试数据失败:', error)
    }
  }

  /**
   * 获取数据库实例
   */
  getDatabase(): Database.Database {
    if (!this.db) {
      throw new Error('数据库未初始化')
    }
    return this.db
  }

  /**
   * 获取当前数据库文件路径
   */
  getDatabaseFilePath(): string {
    return this.dbPath
  }

  /**
   * 获取旧数据库文件路径
   */
  getOldDatabaseFilePath(): string {
    return this.oldDbPath
  }

  /**
   * 关闭数据库连接
   */
  async close(): Promise<void> {
    if (this.db) {
      this.db.close()
      this.db = null
      console.log('数据库连接已关闭')
    }
    return Promise.resolve()
  }

  /**
   * 获取所有书籍
   */
  getBooks(): any[] {
    if (!this.db) {
      throw new Error('数据库未初始化')
    }

    const stmt = this.db.prepare('SELECT * FROM books ORDER BY updated_at DESC')
    return stmt.all()
  }

  /**
   * 获取书籍详情
   */
  getBook(id: number): any {
    if (!this.db) {
      throw new Error('数据库未初始化')
    }

    const stmt = this.db.prepare('SELECT * FROM books WHERE id = ?')
    return stmt.get(id)
  }

  /**
   * 添加书籍
   */
  addBook(bookData: any): number {
    if (!this.db) {
      throw new Error('数据库未初始化')
    }

    const now = new Date().toISOString()

    const stmt = this.db.prepare(`
      INSERT INTO books (
        title, author, isbn, file_path, file_format, file_size,
        cover_image, description, publisher, publish_date, language,
        total_pages, word_count, reading_status, current_page,
        reading_progress_percent, last_read_time, import_time,
        tags, metadata_json, created_at, updated_at, sync_version
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `)

    const result = stmt.run(
      bookData.title || null,
      bookData.author || null,
      bookData.isbn || null,
      bookData.file_path || null,
      bookData.file_format || null,
      bookData.file_size || null,
      bookData.cover_image || null,
      bookData.description || null,
      bookData.publisher || null,
      bookData.publish_date || null,
      bookData.language || null,
      bookData.total_pages || null,
      bookData.word_count || null,
      bookData.reading_status || null,
      bookData.current_page || null,
      bookData.reading_progress_percent || null,
      bookData.last_read_time || null,
      bookData.import_time || now,
      bookData.tags || null,
      bookData.metadata_json || null,
      now,
      now,
      bookData.sync_version || 1
    )

    return result.lastInsertRowid as number
  }

  /**
   * 更新书籍
   */
  updateBook(id: number, bookData: any): boolean {
    if (!this.db) {
      throw new Error('数据库未初始化')
    }

    const now = new Date().toISOString()

    // 构建动态更新语句
    const updateFields = []
    const values = []

    // 只更新提供的字段
    if (bookData.title !== undefined) {
      updateFields.push('title = ?')
      values.push(bookData.title)
    }
    if (bookData.author !== undefined) {
      updateFields.push('author = ?')
      values.push(bookData.author)
    }
    if (bookData.isbn !== undefined) {
      updateFields.push('isbn = ?')
      values.push(bookData.isbn)
    }
    if (bookData.file_path !== undefined) {
      updateFields.push('file_path = ?')
      values.push(bookData.file_path)
    }
    if (bookData.file_format !== undefined) {
      updateFields.push('file_format = ?')
      values.push(bookData.file_format)
    }
    if (bookData.file_size !== undefined) {
      updateFields.push('file_size = ?')
      values.push(bookData.file_size)
    }
    if (bookData.cover_image !== undefined) {
      updateFields.push('cover_image = ?')
      values.push(bookData.cover_image)
    }
    if (bookData.description !== undefined) {
      updateFields.push('description = ?')
      values.push(bookData.description)
    }
    if (bookData.publisher !== undefined) {
      updateFields.push('publisher = ?')
      values.push(bookData.publisher)
    }
    if (bookData.publish_date !== undefined) {
      updateFields.push('publish_date = ?')
      values.push(bookData.publish_date)
    }
    if (bookData.language !== undefined) {
      updateFields.push('language = ?')
      values.push(bookData.language)
    }
    if (bookData.total_pages !== undefined) {
      updateFields.push('total_pages = ?')
      values.push(bookData.total_pages)
    }
    if (bookData.word_count !== undefined) {
      updateFields.push('word_count = ?')
      values.push(bookData.word_count)
    }
    if (bookData.reading_status !== undefined) {
      updateFields.push('reading_status = ?')
      values.push(bookData.reading_status)
    }
    if (bookData.current_page !== undefined) {
      updateFields.push('current_page = ?')
      values.push(bookData.current_page)
    }
    if (bookData.reading_progress_percent !== undefined) {
      updateFields.push('reading_progress_percent = ?')
      values.push(bookData.reading_progress_percent)
    }
    if (bookData.last_read_time !== undefined) {
      updateFields.push('last_read_time = ?')
      values.push(bookData.last_read_time)
    }
    if (bookData.tags !== undefined) {
      updateFields.push('tags = ?')
      values.push(bookData.tags)
    }
    if (bookData.metadata_json !== undefined) {
      updateFields.push('metadata_json = ?')
      values.push(bookData.metadata_json)
    }
    if (bookData.deleted_at !== undefined) {
      updateFields.push('deleted_at = ?')
      values.push(bookData.deleted_at)
    }
    if (bookData.sync_version !== undefined) {
      updateFields.push('sync_version = ?')
      values.push(bookData.sync_version)
    }

    // 总是更新updated_at
    updateFields.push('updated_at = ?')
    values.push(now)

    if (updateFields.length === 1) {
      // 只有updated_at，没有实际更新
      return false
    }

    const stmt = this.db.prepare(`
      UPDATE books SET ${updateFields.join(', ')}
      WHERE id = ?
    `)

    values.push(id)
    const result = stmt.run(...values)

    return result.changes > 0
  }

  /**
   * 删除书籍
   */
  deleteBook(id: number): boolean {
    if (!this.db) {
      throw new Error('数据库未初始化')
    }

    const stmt = this.db.prepare('DELETE FROM books WHERE id = ?')
    const result = stmt.run(id)

    return result.changes > 0
  }

  /**
   * 获取书籍的书签
   */
  getBookmarks(bookId: string): any[] {
    if (!this.db) {
      throw new Error('数据库未初始化')
    }

    const stmt = this.db.prepare('SELECT * FROM bookmarks WHERE book_id = ? ORDER BY created_at DESC')
    return stmt.all(bookId)
  }

  /**
   * 添加书签
   */
  addBookmark(bookmarkData: any): string {
    if (!this.db) {
      throw new Error('数据库未初始化')
    }

    const id = uuidv4()
    const now = Date.now()

    const stmt = this.db.prepare(`
      INSERT INTO bookmarks (
        id, book_id, position, title, content, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?)
    `)

    stmt.run(
      id,
      bookmarkData.bookId,
      bookmarkData.position,
      bookmarkData.title || '',
      bookmarkData.content || '',
      now,
      now
    )

    return id
  }

  /**
   * 获取设置
   */
  getSetting(key: string): any {
    if (!this.db) {
      throw new Error('数据库未初始化')
    }

    const stmt = this.db.prepare('SELECT value FROM settings WHERE key = ?')
    const result = stmt.get(key)

    return result ? JSON.parse(result.value) : null
  }

  /**
   * 保存设置
   */
  saveSetting(key: string, value: any): boolean {
    if (!this.db) {
      throw new Error('数据库未初始化')
    }

    const stmt = this.db.prepare(`
      INSERT OR REPLACE INTO settings (key, value, updated_at)
      VALUES (?, ?, ?)
    `)

    const result = stmt.run(key, JSON.stringify(value), Date.now())

    return result.changes > 0
  }
}
