/**
 * 清理数据库中的测试数据脚本
 * 删除三体、1984、红楼梦等测试图书，保留真实导入的图书
 */

const { spawn } = require('child_process')
const path = require('path')

console.log('🧹 开始清理数据库中的测试数据...\n')

// 数据库路径
const dbPath = path.join(__dirname, 'database', 'yu-reader.db')
console.log('数据库路径:', dbPath)

// 要删除的测试图书标题
const testBookTitles = [
  '三体',
  '活着', 
  '百年孤独',
  '1984',
  '红楼梦'
]

// 使用Electron的better-sqlite3来操作数据库
// 由于Node.js版本问题，我们通过Electron进程来执行
const electronScript = `
const { app } = require('electron')
const Database = require('better-sqlite3')
const path = require('path')

app.whenReady().then(() => {
  const dbPath = path.join(__dirname, 'database', 'yu-reader.db')
  console.log('连接数据库:', dbPath)
  
  try {
    const db = new Database(dbPath)
    
    // 查询当前所有图书
    console.log('\\n📚 清理前的图书列表:')
    const allBooks = db.prepare('SELECT id, title, author FROM books ORDER BY id').all()
    allBooks.forEach((book, index) => {
      console.log(\`\${index + 1}. ID:\${book.id} - \${book.title} - \${book.author}\`)
    })
    
    // 删除测试数据
    const testTitles = ['三体', '活着', '百年孤独', '1984', '红楼梦']
    const deleteStmt = db.prepare('DELETE FROM books WHERE title = ?')
    
    let deletedCount = 0
    testTitles.forEach(title => {
      const result = deleteStmt.run(title)
      if (result.changes > 0) {
        console.log(\`✅ 删除测试图书: \${title}\`)
        deletedCount += result.changes
      }
    })
    
    console.log(\`\\n🗑️  总共删除了 \${deletedCount} 本测试图书\`)
    
    // 查询清理后的图书
    console.log('\\n📚 清理后的图书列表:')
    const remainingBooks = db.prepare('SELECT id, title, author FROM books ORDER BY id').all()
    if (remainingBooks.length > 0) {
      remainingBooks.forEach((book, index) => {
        console.log(\`\${index + 1}. ID:\${book.id} - \${book.title} - \${book.author}\`)
      })
    } else {
      console.log('数据库中没有剩余图书')
    }
    
    db.close()
    console.log('\\n✅ 数据库清理完成！')
    
  } catch (error) {
    console.error('❌ 清理失败:', error.message)
  }
  
  app.quit()
})
`

// 将脚本写入临时文件
const fs = require('fs')
const tempScriptPath = path.join(__dirname, 'temp-clean-script.js')
fs.writeFileSync(tempScriptPath, electronScript)

// 执行Electron脚本
const electronProcess = spawn('npx', ['electron', tempScriptPath], {
  cwd: __dirname,
  stdio: 'inherit'
})

electronProcess.on('close', (code) => {
  // 清理临时文件
  try {
    fs.unlinkSync(tempScriptPath)
  } catch (error) {
    // 忽略删除临时文件的错误
  }
  
  if (code === 0) {
    console.log('\n🎉 数据库清理完成！请重启应用查看效果。')
  } else {
    console.log('\n❌ 清理过程中出现错误，退出码:', code)
  }
})

electronProcess.on('error', (error) => {
  console.error('❌ 执行清理脚本失败:', error.message)
})
