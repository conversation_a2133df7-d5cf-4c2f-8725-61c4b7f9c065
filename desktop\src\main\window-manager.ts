/**
 * 窗口管理器
 * 负责创建和管理应用窗口
 */

import { BrowserWindow, screen, shell } from 'electron'
import { join } from 'path'

export class WindowManager {
  private mainWindow: BrowserWindow | null = null

  /**
   * 创建主窗口
   */
  async createMainWindow(): Promise<BrowserWindow> {
    // 获取主显示器信息
    const primaryDisplay = screen.getPrimaryDisplay()
    const { width, height } = primaryDisplay.workAreaSize

    // 计算窗口大小和位置
    const windowWidth = Math.min(1200, Math.floor(width * 0.8))
    const windowHeight = Math.min(800, Math.floor(height * 0.8))
    const x = Math.floor((width - windowWidth) / 2)
    const y = Math.floor((height - windowHeight) / 2)

    // 创建浏览器窗口
    this.mainWindow = new BrowserWindow({
      width: windowWidth,
      height: windowHeight,
      x,
      y,
      minWidth: 800,
      minHeight: 600,
      show: true, // 立即显示窗口进行测试
      frame: true,
      titleBarStyle: process.platform === 'darwin' ? 'hiddenInset' : 'default',
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        enableRemoteModule: false,
        preload: join(__dirname, '../preload/index.js'),
        webSecurity: true, // 启用web安全
        allowRunningInsecureContent: false, // 禁止不安全内容
        experimentalFeatures: false,
        sandbox: false, // 保持false以允许preload脚本工作
        spellcheck: false // 禁用拼写检查以提高性能
      },
      icon: this.getAppIcon()
    })

    // 窗口准备显示时显示窗口 - 必须在加载内容之前注册
    this.mainWindow.once('ready-to-show', () => {
      this.mainWindow?.show()
      this.mainWindow?.focus()
      this.mainWindow?.moveTop()
      this.mainWindow?.setAlwaysOnTop(true)
      this.mainWindow?.center() // 居中显示
      setTimeout(() => {
        this.mainWindow?.setAlwaysOnTop(false)
      }, 2000) // 延长置顶时间
      console.log('窗口已显示')
      console.log('窗口位置:', this.mainWindow?.getBounds())
      console.log('窗口是否可见:', this.mainWindow?.isVisible())
      console.log('窗口是否最小化:', this.mainWindow?.isMinimized())

      // 强制刷新窗口
      this.mainWindow?.webContents.reload()
    })

    // 添加错误处理
    this.mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription, validatedURL) => {
      console.error('页面加载失败:', errorCode, errorDescription, validatedURL)
      // 即使加载失败也显示窗口，这样用户可以看到错误
      this.mainWindow?.show()
    })

    // 加载渲染进程
    const nodeEnv = (process.env.NODE_ENV || '').trim()
    console.log('当前环境变量 NODE_ENV:', `"${process.env.NODE_ENV}"`)
    console.log('清理后的环境变量:', `"${nodeEnv}"`)
    console.log('是否为开发环境:', nodeEnv === 'development')

    if (nodeEnv === 'development') {
      // 开发环境加载开发服务器 - 使用正确的端口号
      const devUrl = 'http://127.0.0.1:5174'
      console.log('开发模式，加载URL:', devUrl)
      await this.mainWindow.loadURL(devUrl)
      // 打开开发者工具
      this.mainWindow.webContents.openDevTools()
    } else {
      // 生产环境加载构建后的文件
      const htmlPath = join(__dirname, '../renderer/index.html')
      console.log('生产模式，加载文件:', htmlPath)
      await this.mainWindow.loadFile(htmlPath)
    }

    // 窗口关闭事件
    this.mainWindow.on('closed', () => {
      this.mainWindow = null
    })

    // 阻止新窗口打开，改为在默认浏览器中打开
    this.mainWindow.webContents.setWindowOpenHandler(({ url }) => {
      shell.openExternal(url)
      return { action: 'deny' }
    })

    // 阻止导航到外部链接
    this.mainWindow.webContents.on('will-navigate', (event, navigationUrl) => {
      const parsedUrl = new URL(navigationUrl)
      
      // 允许本地文件和开发服务器
      if (parsedUrl.protocol === 'file:' || 
          (parsedUrl.protocol === 'http:' && parsedUrl.hostname === '127.0.0.1')) {
        return
      }
      
      // 阻止其他导航并在外部浏览器打开
      event.preventDefault()
      shell.openExternal(navigationUrl)
    })

    console.log('主窗口创建完成')

    // 额外的强制显示逻辑
    setTimeout(() => {
      if (this.mainWindow && !this.mainWindow.isVisible()) {
        console.log('窗口仍然不可见，强制显示...')
        this.mainWindow.show()
        this.mainWindow.focus()
        this.mainWindow.center()
        this.mainWindow.setAlwaysOnTop(true)
        setTimeout(() => {
          this.mainWindow?.setAlwaysOnTop(false)
        }, 3000)
      }
    }, 2000)

    return this.mainWindow
  }

  /**
   * 获取主窗口实例
   */
  getMainWindow(): BrowserWindow | null {
    return this.mainWindow
  }

  /**
   * 最小化窗口
   */
  minimizeWindow(): void {
    this.mainWindow?.minimize()
  }

  /**
   * 最大化/还原窗口
   */
  toggleMaximizeWindow(): void {
    if (this.mainWindow?.isMaximized()) {
      this.mainWindow.unmaximize()
    } else {
      this.mainWindow?.maximize()
    }
  }

  /**
   * 关闭窗口
   */
  closeWindow(): void {
    this.mainWindow?.close()
  }

  /**
   * 切换全屏模式
   */
  toggleFullscreen(): void {
    const isFullscreen = this.mainWindow?.isFullScreen() ?? false
    this.mainWindow?.setFullScreen(!isFullscreen)
  }

  /**
   * 获取应用图标路径
   */
  private getAppIcon(): string | undefined {
    const iconPath = join(__dirname, '../../build')
    
    switch (process.platform) {
      case 'win32':
        return join(iconPath, 'icon.ico')
      case 'darwin':
        return join(iconPath, 'icon.icns')
      case 'linux':
        return join(iconPath, 'icon.png')
      default:
        return undefined
    }
  }
}
