/**
 * 集成测试脚本
 * 测试电子书阅读器的完整功能流程
 */

const fs = require('fs')
const path = require('path')

class IntegrationTester {
  constructor() {
    this.testResults = {
      passed: 0,
      failed: 0,
      tests: []
    }
  }

  log(name, passed, message = '') {
    const result = { name, passed, message, timestamp: new Date().toISOString() }
    this.testResults.tests.push(result)
    
    if (passed) {
      this.testResults.passed++
      console.log(`✅ ${name}`)
    } else {
      this.testResults.failed++
      console.log(`❌ ${name}: ${message}`)
    }
  }

  // 测试项目结构
  async testProjectStructure() {
    console.log('\n📁 测试项目结构...')
    
    const requiredFiles = [
      'package.json',
      'src/main/app.ts',
      'src/renderer/main.ts',
      'src/preload/index.ts',
      'src/shared/database/repositories/BookRepository.ts',
      'src/shared/services/MetadataExtractor.ts',
      'src/shared/services/BookImportService.ts',
      'src/renderer/store/bookshelf.ts',
      'src/renderer/store/unifiedLibrary.ts',
      'src/renderer/views/library/UnifiedLibraryView.vue',
      'src/renderer/views/bookshelf/ImportView.vue',
      'src/renderer/views/bookshelf/SearchView.vue',
      'src/renderer/components/BatchOperations.vue',
      'src/renderer/components/BookDetailDialog.vue'
    ]

    for (const file of requiredFiles) {
      const exists = fs.existsSync(path.join(__dirname, file))
      this.log(`项目文件存在: ${file}`, exists)
    }
  }

  // 测试构建输出
  async testBuildOutput() {
    console.log('\n🔨 测试构建输出...')
    
    const buildFiles = [
      'dist/main/app.js',
      'dist/preload/index.js'
    ]

    for (const file of buildFiles) {
      const exists = fs.existsSync(path.join(__dirname, file))
      this.log(`构建文件存在: ${file}`, exists)
      
      if (exists) {
        const stats = fs.statSync(path.join(__dirname, file))
        const sizeOK = stats.size > 0
        this.log(`构建文件大小正常: ${file}`, sizeOK, `${stats.size} bytes`)
      }
    }
  }

  // 测试配置文件
  async testConfiguration() {
    console.log('\n⚙️ 测试配置文件...')
    
    try {
      const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'))
      
      // 检查必要的依赖
      const requiredDeps = ['electron', 'vue', 'element-plus', 'pinia']
      for (const dep of requiredDeps) {
        const hasDepOrDevDep = packageJson.dependencies?.[dep] || packageJson.devDependencies?.[dep]
        this.log(`依赖存在: ${dep}`, !!hasDepOrDevDep)
      }
      
      // 检查脚本
      const requiredScripts = ['dev', 'build', 'start:dev']
      for (const script of requiredScripts) {
        const hasScript = !!packageJson.scripts?.[script]
        this.log(`脚本存在: ${script}`, hasScript)
      }
      
    } catch (error) {
      this.log('package.json解析', false, error.message)
    }
  }

  // 测试TypeScript配置
  async testTypeScriptConfig() {
    console.log('\n📝 测试TypeScript配置...')
    
    const tsConfigFiles = ['tsconfig.json', 'tsconfig.node.json']
    
    for (const file of tsConfigFiles) {
      try {
        const exists = fs.existsSync(file)
        this.log(`TypeScript配置存在: ${file}`, exists)
        
        if (exists) {
          const config = JSON.parse(fs.readFileSync(file, 'utf8'))
          const hasCompilerOptions = !!config.compilerOptions
          this.log(`TypeScript配置有效: ${file}`, hasCompilerOptions)
        }
      } catch (error) {
        this.log(`TypeScript配置解析: ${file}`, false, error.message)
      }
    }
  }

  // 测试Vue组件语法
  async testVueComponents() {
    console.log('\n🎨 测试Vue组件语法...')
    
    const vueFiles = [
      'src/renderer/views/library/UnifiedLibraryView.vue',
      'src/renderer/views/bookshelf/ImportView.vue',
      'src/renderer/views/bookshelf/SearchView.vue',
      'src/renderer/components/BatchOperations.vue',
      'src/renderer/components/BookDetailDialog.vue'
    ]

    for (const file of vueFiles) {
      try {
        if (fs.existsSync(file)) {
          const content = fs.readFileSync(file, 'utf8')
          
          // 检查Vue组件基本结构
          const hasTemplate = content.includes('<template>')
          const hasScript = content.includes('<script')
          const hasStyle = content.includes('<style')
          
          this.log(`Vue组件结构完整: ${path.basename(file)}`, hasTemplate && hasScript)
          
          // 检查TypeScript setup语法
          const hasSetup = content.includes('setup')
          this.log(`Vue组件使用setup语法: ${path.basename(file)}`, hasSetup)
          
          // 检查导入语句
          const hasImports = content.includes('import')
          this.log(`Vue组件有导入语句: ${path.basename(file)}`, hasImports)
        }
      } catch (error) {
        this.log(`Vue组件检查: ${path.basename(file)}`, false, error.message)
      }
    }
  }

  // 测试服务类
  async testServices() {
    console.log('\n🔧 测试服务类...')
    
    const serviceFiles = [
      'src/shared/services/MetadataExtractor.ts',
      'src/shared/services/BookImportService.ts',
      'src/shared/services/BatchExportService.ts'
    ]

    for (const file of serviceFiles) {
      try {
        if (fs.existsSync(file)) {
          const content = fs.readFileSync(file, 'utf8')
          
          // 检查类定义
          const hasClass = content.includes('class') || content.includes('export')
          this.log(`服务类定义正确: ${path.basename(file)}`, hasClass)
          
          // 检查方法定义
          const hasMethods = content.includes('async') || content.includes('function')
          this.log(`服务类有方法: ${path.basename(file)}`, hasMethods)
          
          // 检查错误处理
          const hasErrorHandling = content.includes('try') && content.includes('catch')
          this.log(`服务类有错误处理: ${path.basename(file)}`, hasErrorHandling)
        }
      } catch (error) {
        this.log(`服务类检查: ${path.basename(file)}`, false, error.message)
      }
    }
  }

  // 测试数据库相关文件
  async testDatabase() {
    console.log('\n🗄️ 测试数据库相关文件...')
    
    const dbFiles = [
      'src/shared/database/repositories/BookRepository.ts',
      'src/shared/database/repositories/BaseRepository.ts',
      'src/shared/types/database.ts'
    ]

    for (const file of dbFiles) {
      try {
        if (fs.existsSync(file)) {
          const content = fs.readFileSync(file, 'utf8')
          
          // 检查数据库相关代码
          const hasDbCode = content.includes('database') || content.includes('sqlite') || content.includes('sql')
          this.log(`数据库代码存在: ${path.basename(file)}`, hasDbCode)
          
          // 检查类型定义
          const hasTypes = content.includes('interface') || content.includes('type')
          this.log(`类型定义存在: ${path.basename(file)}`, hasTypes)
        }
      } catch (error) {
        this.log(`数据库文件检查: ${path.basename(file)}`, false, error.message)
      }
    }
  }

  // 测试状态管理
  async testStateManagement() {
    console.log('\n📦 测试状态管理...')
    
    try {
      if (fs.existsSync('src/renderer/store/bookshelf.ts')) {
        const content = fs.readFileSync('src/renderer/store/bookshelf.ts', 'utf8')
        
        // 检查Pinia store
        const hasPinia = content.includes('defineStore') || content.includes('pinia')
        this.log('Pinia store定义', hasPinia)
        
        // 检查状态定义
        const hasState = content.includes('ref(') || content.includes('reactive(')
        this.log('状态定义存在', hasState)
        
        // 检查计算属性
        const hasComputed = content.includes('computed(')
        this.log('计算属性存在', hasComputed)
        
        // 检查方法定义
        const hasMethods = content.includes('const ') && content.includes('=')
        this.log('方法定义存在', hasMethods)
      }
    } catch (error) {
      this.log('状态管理检查', false, error.message)
    }
  }

  // 测试工具函数
  async testUtilities() {
    console.log('\n🛠️ 测试工具函数...')
    
    const utilFiles = [
      'src/shared/utils/ErrorHandler.ts',
      'src/shared/utils/PerformanceOptimizer.ts',
      'src/shared/utils/ValidationUtils.ts'
    ]

    for (const file of utilFiles) {
      try {
        if (fs.existsSync(file)) {
          const content = fs.readFileSync(file, 'utf8')
          
          // 检查导出
          const hasExports = content.includes('export')
          this.log(`工具函数导出: ${path.basename(file)}`, hasExports)
          
          // 检查函数定义
          const hasFunctions = content.includes('function') || content.includes('=>')
          this.log(`工具函数定义: ${path.basename(file)}`, hasFunctions)
        }
      } catch (error) {
        this.log(`工具函数检查: ${path.basename(file)}`, false, error.message)
      }
    }
  }

  // 生成测试报告
  generateReport() {
    console.log('\n📋 生成测试报告...')
    
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        total: this.testResults.passed + this.testResults.failed,
        passed: this.testResults.passed,
        failed: this.testResults.failed,
        successRate: ((this.testResults.passed / (this.testResults.passed + this.testResults.failed)) * 100).toFixed(1)
      },
      tests: this.testResults.tests
    }

    // 写入报告文件
    fs.writeFileSync('integration-test-report.json', JSON.stringify(report, null, 2))
    
    console.log('\n📊 集成测试结果汇总:')
    console.log(`✅ 通过: ${report.summary.passed}`)
    console.log(`❌ 失败: ${report.summary.failed}`)
    console.log(`📈 成功率: ${report.summary.successRate}%`)
    
    if (this.testResults.failed > 0) {
      console.log('\n❌ 失败的测试:')
      this.testResults.tests
        .filter(test => !test.passed)
        .forEach(test => console.log(`  - ${test.name}: ${test.message}`))
    }
    
    console.log(`\n📄 详细报告已保存到: integration-test-report.json`)
    
    return report
  }

  // 运行所有测试
  async runAllTests() {
    console.log('🚀 开始集成测试...')
    
    await this.testProjectStructure()
    await this.testBuildOutput()
    await this.testConfiguration()
    await this.testTypeScriptConfig()
    await this.testVueComponents()
    await this.testServices()
    await this.testDatabase()
    await this.testStateManagement()
    await this.testUtilities()
    
    const report = this.generateReport()
    
    console.log('\n🎉 集成测试完成!')
    
    return report
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const tester = new IntegrationTester()
  tester.runAllTests().then(report => {
    process.exit(report.summary.failed > 0 ? 1 : 0)
  }).catch(error => {
    console.error('集成测试运行失败:', error)
    process.exit(1)
  })
}

module.exports = IntegrationTester
