/**
 * 简单批量导入脚本
 * 直接通过文件系统扫描并生成导入命令
 */

const fs = require('fs')
const path = require('path')

console.log('📚 简单批量导入脚本启动...\n')

// 支持的文件格式
const supportedFormats = ['.txt', '.epub', '.pdf', '.mobi', '.azw3']

// 扫描目录
const booksDir = path.join(__dirname, 'books')

/**
 * 递归扫描目录获取所有电子书文件
 */
function scanDirectory(dir) {
  const files = []
  
  try {
    const items = fs.readdirSync(dir)
    
    for (const item of items) {
      const fullPath = path.join(dir, item)
      const stat = fs.statSync(fullPath)
      
      if (stat.isDirectory()) {
        // 递归扫描子目录
        files.push(...scanDirectory(fullPath))
      } else if (stat.isFile()) {
        const ext = path.extname(item).toLowerCase()
        if (supportedFormats.includes(ext)) {
          files.push({
            path: fullPath,
            name: item,
            size: stat.size,
            format: ext.substring(1), // 移除点号
            directory: path.relative(booksDir, dir) || 'root'
          })
        }
      }
    }
  } catch (error) {
    console.error(`❌ 扫描目录失败 ${dir}:`, error.message)
  }
  
  return files
}

// 主执行逻辑
try {
  console.log(`📁 扫描目录: ${booksDir}`)
  
  if (!fs.existsSync(booksDir)) {
    console.error(`❌ 目录不存在: ${booksDir}`)
    process.exit(1)
  }
  
  const files = scanDirectory(booksDir)
  
  console.log(`\n📊 扫描结果:`)
  console.log(`  找到 ${files.length} 个电子书文件`)
  
  // 按格式分组统计
  const formatStats = {}
  files.forEach(file => {
    formatStats[file.format] = (formatStats[file.format] || 0) + 1
  })
  
  console.log(`  格式分布:`)
  Object.entries(formatStats).forEach(([format, count]) => {
    console.log(`    ${format.toUpperCase()}: ${count} 个`)
  })
  
  if (files.length === 0) {
    console.log(`\n⚠️  没有找到支持的电子书文件`)
    console.log(`支持的格式: ${supportedFormats.join(', ')}`)
    process.exit(0)
  }
  
  // 显示文件列表
  console.log(`\n📋 文件列表:`)
  files.forEach((file, index) => {
    console.log(`  ${index + 1}. ${file.name} (${file.format.toUpperCase()}, ${(file.size / 1024).toFixed(1)} KB)`)
  })
  
  // 生成简化的导入脚本，直接输出到控制台
  console.log(`\n🎯 请复制以下脚本到 Electron 应用的开发者工具控制台中执行:\n`)
  console.log(`${'='.repeat(80)}`)
  
  // 生成浏览器控制台脚本
  const browserScript = `
// 批量导入脚本 - 在 Electron 应用开发者工具中执行
console.log('🎯 开始批量导入 ${files.length} 本图书...');

const filesToImport = ${JSON.stringify(files.map(f => f.path), null, 2)};

async function batchImport() {
  let successCount = 0;
  let failCount = 0;
  
  // 检查导入前状态
  const beforeBooks = await window.electronAPI.book.list();
  console.log(\`导入前数据库中有 \${beforeBooks.length} 本图书\`);
  
  for (let i = 0; i < filesToImport.length; i++) {
    const filePath = filesToImport[i];
    const fileName = filePath.split(/[\\\\\\/]/).pop();
    
    console.log(\`\\n[\${i + 1}/\${filesToImport.length}] 正在导入: \${fileName}\`);
    console.log(\`  路径: \${filePath}\`);
    
    try {
      const result = await window.electronAPI.book.add(filePath);
      console.log(\`  ✅ 导入成功: \${result.title || fileName}\`);
      console.log(\`     ID: \${result.id}, 作者: \${result.author || '未知'}\`);
      console.log(\`     格式: \${result.file_format}, 大小: \${result.file_size} 字节\`);
      successCount++;
      
      // 短暂延迟
      await new Promise(resolve => setTimeout(resolve, 100));
      
    } catch (error) {
      console.error(\`  ❌ 导入失败: \${error.message}\`);
      failCount++;
    }
  }
  
  // 检查导入后状态
  const afterBooks = await window.electronAPI.book.list();
  console.log(\`\\n📊 导入完成统计:\`);
  console.log(\`  总计: \${filesToImport.length} 本\`);
  console.log(\`  成功: \${successCount} 本\`);
  console.log(\`  失败: \${failCount} 本\`);
  console.log(\`  导入前: \${beforeBooks.length} 本\`);
  console.log(\`  导入后: \${afterBooks.length} 本\`);
  console.log(\`  实际新增: \${afterBooks.length - beforeBooks.length} 本\`);
  
  // 显示新导入的图书
  if (afterBooks.length > beforeBooks.length) {
    console.log(\`\\n📚 新导入的图书:\`);
    const newBooks = afterBooks.slice(beforeBooks.length);
    newBooks.forEach((book, index) => {
      console.log(\`  \${index + 1}. 《\${book.title}》 - \${book.author || '未知作者'}\`);
      console.log(\`     格式: \${book.file_format} | 大小: \${book.file_size} 字节\`);
    });
  }
  
  console.log(\`\\n🎉 批量导入完成!\`);
  return { total: filesToImport.length, success: successCount, failed: failCount };
}

// 执行导入
batchImport().then(result => {
  console.log('✅ 导入任务完成!', result);
}).catch(error => {
  console.error('💥 导入任务失败:', error);
});`

  console.log(browserScript)
  console.log(`${'='.repeat(80)}\n`)
  
  console.log(`📋 操作步骤:`)
  console.log(`1. 确保 Electron 应用正在运行`)
  console.log(`2. 在应用中按 F12 打开开发者工具`)
  console.log(`3. 切换到 Console 标签页`)
  console.log(`4. 复制上面的脚本内容`)
  console.log(`5. 粘贴到控制台并按 Enter 执行`)
  console.log(`6. 观察导入过程和结果`)
  
  // 保存脚本到文件
  const scriptPath = path.join(__dirname, 'console-import-script.js')
  fs.writeFileSync(scriptPath, browserScript, 'utf8')
  console.log(`\n💾 脚本已保存到: ${scriptPath}`)
  
} catch (error) {
  console.error('💥 脚本执行失败:', error)
  process.exit(1)
}
