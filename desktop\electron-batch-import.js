/**
 * 通过 Electron 主进程直接执行批量导入
 * 读取控制台脚本并在渲染进程中执行
 */

const fs = require('fs')
const path = require('path')
const { exec } = require('child_process')

console.log('🚀 通过 Electron 执行批量导入...\n')

// 读取控制台脚本
const scriptPath = path.join(__dirname, 'console-import-script.js')

if (!fs.existsSync(scriptPath)) {
  console.error('❌ 控制台脚本不存在，请先运行 simple-batch-import.js')
  process.exit(1)
}

const scriptContent = fs.readFileSync(scriptPath, 'utf8')

// 提取文件列表
const filesMatch = scriptContent.match(/const filesToImport = (\[[\s\S]*?\]);/)
if (!filesMatch) {
  console.error('❌ 无法解析脚本中的文件列表')
  process.exit(1)
}

let filesToImport
try {
  filesToImport = JSON.parse(filesMatch[1])
} catch (error) {
  console.error('❌ 解析文件列表失败:', error.message)
  process.exit(1)
}

console.log(`📊 准备导入 ${filesToImport.length} 本图书`)

// 创建一个简化的执行脚本，直接通过 Node.js 调用
const executeScript = `
const { BrowserWindow } = require('electron');

async function executeImport() {
  const windows = BrowserWindow.getAllWindows();
  if (windows.length === 0) {
    console.error('❌ 没有找到 Electron 窗口');
    return;
  }

  const mainWindow = windows[0];
  console.log('✅ 找到主窗口，开始执行导入');

  const script = \`${scriptContent.replace(/`/g, '\\`').replace(/\$/g, '\\$')}\`;
  
  try {
    const result = await mainWindow.webContents.executeJavaScript(script);
    console.log('✅ 导入脚本执行完成:', result);
    return result;
  } catch (error) {
    console.error('❌ 执行导入脚本失败:', error);
    throw error;
  }
}

executeImport().then(result => {
  console.log('🎉 批量导入完成!', result);
}).catch(error => {
  console.error('💥 批量导入失败:', error);
});
`

// 保存执行脚本
const execScriptPath = path.join(__dirname, 'exec-import.js')
fs.writeFileSync(execScriptPath, executeScript, 'utf8')

console.log(`✅ 执行脚本已生成: ${execScriptPath}`)

// 显示要导入的文件（前10个）
console.log(`\n📋 即将导入的文件（前10个）:`)
filesToImport.slice(0, 10).forEach((filePath, index) => {
  const fileName = path.basename(filePath)
  const fileSize = fs.existsSync(filePath) ? fs.statSync(filePath).size : 0
  console.log(`  ${index + 1}. ${fileName} (${(fileSize / 1024).toFixed(1)} KB)`)
})

if (filesToImport.length > 10) {
  console.log(`  ... 还有 ${filesToImport.length - 10} 个文件`)
}

console.log(`\n🎯 现在直接在 Electron 应用中执行导入...`)

// 检查 Electron 应用是否在运行
exec('tasklist /FI "IMAGENAME eq electron.exe"', (error, stdout, stderr) => {
  if (error) {
    console.error('❌ 检查 Electron 进程失败:', error.message)
    return
  }

  if (stdout.includes('electron.exe')) {
    console.log('✅ 检测到 Electron 应用正在运行')
    
    // 创建一个简单的注入脚本
    const injectScript = `
// 直接在当前页面执行导入
(function() {
  ${scriptContent}
})();
`
    
    console.log(`\n📋 请在 Electron 应用的开发者工具控制台中执行以下脚本:`)
    console.log(`${'='.repeat(80)}`)
    console.log(injectScript)
    console.log(`${'='.repeat(80)}`)
    
    // 保存注入脚本
    const injectScriptPath = path.join(__dirname, 'inject-import.js')
    fs.writeFileSync(injectScriptPath, injectScript, 'utf8')
    console.log(`\n💾 注入脚本已保存到: ${injectScriptPath}`)
    
  } else {
    console.log('⚠️  未检测到 Electron 应用，请先启动应用')
    console.log('启动命令: $env:NODE_ENV="development"; npx electron .')
  }
})

console.log(`\n📝 操作说明:`)
console.log(`1. 确保 Electron 应用正在运行`)
console.log(`2. 在应用中按 F12 打开开发者工具`)
console.log(`3. 切换到 Console 标签页`)
console.log(`4. 复制并粘贴上面的脚本内容`)
console.log(`5. 按 Enter 执行，观察导入过程`)
console.log(`6. 导入完成后可以在图书列表中查看结果`)

console.log(`\n💡 提示: 导入过程可能需要几分钟，请耐心等待`)
console.log(`📊 总计将导入 ${filesToImport.length} 本图书，包括:`)

// 统计格式分布
const formatStats = {}
filesToImport.forEach(filePath => {
  const ext = path.extname(filePath).toLowerCase().substring(1)
  formatStats[ext.toUpperCase()] = (formatStats[ext.toUpperCase()] || 0) + 1
})

Object.entries(formatStats).forEach(([format, count]) => {
  console.log(`  ${format}: ${count} 本`)
})
