/**
 * 创建真实的测试电子书文件
 * 用于验证图书导入和阅读功能
 */

const fs = require('fs')
const path = require('path')

console.log('📚 创建真实的测试电子书文件...\n')

// 创建books目录
const booksDir = path.join(__dirname, 'books')
const txtDir = path.join(booksDir, 'txt')
const epubDir = path.join(booksDir, 'epub')
const pdfDir = path.join(booksDir, 'pdf')

// 确保目录存在
[booksDir, txtDir, epubDir, pdfDir].forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true })
    console.log(`✅ 创建目录: ${dir}`)
  }
})

// 创建TXT格式的测试图书
const txtBooks = [
  {
    filename: '人工智能简史.txt',
    title: '人工智能简史',
    author: '张明华',
    content: `人工智能简史

作者：张明华

第一章 人工智能的起源

人工智能（Artificial Intelligence，简称AI）是计算机科学的一个分支，它企图了解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。

人工智能的概念最早可以追溯到古希腊神话中的机械人。然而，现代意义上的人工智能研究始于20世纪40年代。1950年，英国数学家艾伦·图灵发表了著名的论文《计算机器与智能》，提出了著名的"图灵测试"。

第二章 机器学习的发展

机器学习是人工智能的一个重要分支，它使计算机能够在没有明确编程的情况下学习。机器学习算法通过分析大量数据来识别模式，并使用这些模式来对新数据进行预测。

深度学习是机器学习的一个子集，它模仿人脑神经网络的工作方式。深度学习在图像识别、自然语言处理和语音识别等领域取得了突破性进展。

第三章 人工智能的应用

今天，人工智能已经广泛应用于各个领域：

1. 医疗诊断：AI可以帮助医生更准确地诊断疾病
2. 自动驾驶：无人驾驶汽车使用AI技术导航和避障
3. 金融服务：AI用于风险评估和欺诈检测
4. 教育：个性化学习系统根据学生需求调整教学内容
5. 娱乐：推荐系统为用户推荐感兴趣的内容

第四章 未来展望

人工智能的未来充满无限可能。随着计算能力的提升和算法的改进，AI将在更多领域发挥重要作用。然而，我们也需要关注AI发展带来的伦理和社会问题，确保AI技术的发展能够造福人类。

结语

人工智能正在改变我们的世界。了解AI的历史、现状和未来趋势，有助于我们更好地适应这个智能化的时代。

（本书为测试用途，内容仅供演示）`
  },
  {
    filename: '现代软件工程.txt',
    title: '现代软件工程',
    author: '王小明',
    content: `现代软件工程

作者：王小明

前言

软件工程是一门研究用工程化方法构建和维护有效的、实用的和高质量的软件的学科。本书将介绍现代软件工程的核心概念、方法和实践。

第一章 软件工程概述

软件工程的目标是在预算和时间限制内，开发出满足用户需求的高质量软件。软件工程包括以下几个主要阶段：

1. 需求分析：理解用户需要什么
2. 系统设计：规划如何构建软件
3. 编码实现：将设计转化为可执行代码
4. 测试验证：确保软件按预期工作
5. 部署维护：将软件交付给用户并持续改进

第二章 敏捷开发方法

敏捷开发是一种迭代式的软件开发方法，强调：
- 个体和互动胜过流程和工具
- 工作的软件胜过详尽的文档
- 客户合作胜过合同谈判
- 响应变化胜过遵循计划

常见的敏捷方法包括Scrum、看板（Kanban）和极限编程（XP）。

第三章 版本控制与协作

版本控制系统如Git帮助开发团队管理代码变更，支持多人协作开发。主要功能包括：
- 跟踪文件变更历史
- 支持分支和合并
- 解决代码冲突
- 备份和恢复

第四章 软件测试

软件测试是确保软件质量的重要环节，包括：
- 单元测试：测试单个组件
- 集成测试：测试组件间的交互
- 系统测试：测试整个系统
- 用户验收测试：验证是否满足用户需求

第五章 持续集成与部署

持续集成（CI）和持续部署（CD）是现代软件开发的重要实践：
- 自动化构建和测试
- 快速反馈和问题发现
- 频繁、可靠的软件发布

结语

现代软件工程强调质量、效率和用户满意度。掌握这些概念和方法，将帮助您成为更优秀的软件开发者。

（本书为测试用途，内容仅供演示）`
  },
  {
    filename: '数据科学实战.txt',
    title: '数据科学实战',
    author: '陈大华',
    content: `数据科学实战

作者：陈大华

导言

数据科学是一个跨学科领域，结合了统计学、计算机科学和领域专业知识，从数据中提取有价值的洞察。

第一章 数据科学基础

数据科学的核心流程包括：
1. 数据收集：获取相关数据
2. 数据清洗：处理缺失值和异常值
3. 数据探索：理解数据的特征和模式
4. 建模分析：应用统计和机器学习方法
5. 结果解释：将发现转化为可行的建议

第二章 数据收集与预处理

数据来源多样化：
- 数据库和数据仓库
- API和网络爬虫
- 传感器和物联网设备
- 社交媒体和网络日志

数据预处理是关键步骤：
- 处理缺失数据
- 数据类型转换
- 特征工程
- 数据标准化

第三章 探索性数据分析

通过可视化和统计分析理解数据：
- 描述性统计
- 数据分布分析
- 相关性分析
- 异常值检测

常用工具：Python（pandas、matplotlib、seaborn）、R、Tableau

第四章 机器学习应用

根据问题类型选择合适的算法：
- 监督学习：分类和回归
- 无监督学习：聚类和降维
- 强化学习：决策优化

模型评估和选择：
- 交叉验证
- 性能指标
- 过拟合和欠拟合

第五章 实战案例

案例1：客户流失预测
- 业务背景和问题定义
- 数据收集和特征工程
- 模型构建和评估
- 结果解释和业务建议

案例2：推荐系统
- 协同过滤算法
- 内容基础推荐
- 混合推荐策略
- 系统评估指标

结语

数据科学是理论与实践相结合的领域。通过不断学习和实践，您将能够从数据中发现有价值的洞察，为业务决策提供支持。

（本书为测试用途，内容仅供演示）`
  }
]

// 创建TXT文件
txtBooks.forEach(book => {
  const filePath = path.join(txtDir, book.filename)
  fs.writeFileSync(filePath, book.content, 'utf8')
  console.log(`✅ 创建TXT图书: ${book.filename}`)
})

console.log(`\n📁 测试图书文件已创建在以下目录:`)
console.log(`   TXT格式: ${txtDir}`)
console.log(`   EPUB格式: ${epubDir} (暂未创建)`)
console.log(`   PDF格式: ${pdfDir} (暂未创建)`)

console.log(`\n🎯 现在您可以使用应用的"添加图书"功能导入这些文件！`)
console.log(`\n💡 导入步骤:`)
console.log(`1. 在应用中点击"添加图书"按钮`)
console.log(`2. 选择 ${txtDir} 目录中的文件`)
console.log(`3. 确认导入`)
console.log(`4. 验证图书是否正确显示`)
