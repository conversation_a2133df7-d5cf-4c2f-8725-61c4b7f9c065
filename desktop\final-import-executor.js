/**
 * 最终导入执行器
 * 直接通过 Electron 应用执行批量导入
 */

const fs = require('fs')
const path = require('path')

console.log('🎯 最终导入执行器启动...\n')

// 读取注入脚本
const injectScriptPath = path.join(__dirname, 'inject-import.js')

if (!fs.existsSync(injectScriptPath)) {
  console.error('❌ 注入脚本不存在，请先运行 electron-batch-import.js')
  process.exit(1)
}

const injectScript = fs.readFileSync(injectScriptPath, 'utf8')

console.log('📊 准备执行批量导入...')
console.log('📋 导入脚本内容:')
console.log('=' * 50)
console.log(injectScript.substring(0, 500) + '...')
console.log('=' * 50)

// 创建一个简化的执行函数
async function executeInElectron() {
  try {
    // 检查是否在 Electron 环境中
    if (typeof window !== 'undefined' && window.electronAPI) {
      console.log('✅ 检测到 Electron 环境，开始执行导入')
      
      // 直接执行导入脚本
      eval(injectScript)
      
    } else {
      console.log('⚠️  不在 Electron 环境中，生成浏览器执行指令')
      
      console.log('\n🎯 请按以下步骤操作:')
      console.log('1. 确保 Electron 应用正在运行')
      console.log('2. 在应用中按 F12 打开开发者工具')
      console.log('3. 切换到 Console 标签页')
      console.log('4. 复制以下完整脚本:')
      console.log('\n' + '='.repeat(80))
      console.log(injectScript)
      console.log('='.repeat(80))
      console.log('\n5. 粘贴到控制台并按 Enter 执行')
      console.log('6. 观察导入过程，等待完成')
    }
    
  } catch (error) {
    console.error('💥 执行失败:', error)
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  executeInElectron()
}

// 导出执行函数
module.exports = { executeInElectron, injectScript }

console.log('\n📝 脚本说明:')
console.log('- 此脚本包含了完整的批量导入逻辑')
console.log('- 将导入 37 本电子书（EPUB: 14本, TXT: 12本, PDF: 11本）')
console.log('- 导入过程会显示详细的进度和结果')
console.log('- 导入完成后会显示新增图书的详细信息')

console.log('\n🚀 现在请在 Electron 应用的开发者工具中执行上述脚本！')
