/**
 * 测试CSP修复和PDF阅读器完整功能
 * 验证Blob URL能否在iframe中正常加载
 */

console.log('🔧 测试CSP修复和PDF阅读器功能...');

async function testCSPAndPdfReader() {
  try {
    console.log('📋 开始完整的PDF阅读器测试...');
    
    // 1. 检查基础API
    if (!window.electronAPI || !window.electronAPI.file) {
      throw new Error('ElectronAPI不可用');
    }
    console.log('✅ ElectronAPI可用');
    
    // 2. 测试文件读取
    const testPdfPath = 'D:\\reader\\desktop\\books\\pdf\\《生物学基础》.pdf';
    console.log('📖 测试PDF文件:', testPdfPath);
    
    const fileExists = await window.electronAPI.file.exists(testPdfPath);
    if (!fileExists) {
      throw new Error('测试PDF文件不存在');
    }
    console.log('✅ PDF文件存在');
    
    // 3. 读取文件内容
    console.log('📥 读取PDF文件内容...');
    const startTime = Date.now();
    const fileBuffer = await window.electronAPI.file.read(testPdfPath);
    const readDuration = Date.now() - startTime;
    
    console.log(`✅ 文件读取成功，耗时: ${readDuration}ms`);
    console.log(`📊 文件大小: ${fileBuffer.byteLength} 字节 (${(fileBuffer.byteLength / 1024).toFixed(1)} KB)`);
    
    // 4. 创建Blob URL
    console.log('🔗 创建Blob URL...');
    const blob = new Blob([fileBuffer], { type: 'application/pdf' });
    const blobUrl = URL.createObjectURL(blob);
    console.log('✅ Blob URL创建成功:', blobUrl);
    
    // 5. 测试CSP策略 - 创建iframe加载Blob URL
    console.log('🛡️ 测试CSP策略和iframe加载...');
    
    const testIframe = document.createElement('iframe');
    testIframe.style.width = '300px';
    testIframe.style.height = '200px';
    testIframe.style.border = '1px solid #ccc';
    testIframe.style.position = 'fixed';
    testIframe.style.top = '10px';
    testIframe.style.right = '10px';
    testIframe.style.zIndex = '9999';
    testIframe.style.backgroundColor = 'white';
    
    // 添加加载事件监听
    const loadPromise = new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('iframe加载超时'));
      }, 10000);
      
      testIframe.onload = () => {
        clearTimeout(timeout);
        console.log('✅ iframe加载成功');
        resolve(true);
      };
      
      testIframe.onerror = (error) => {
        clearTimeout(timeout);
        console.error('❌ iframe加载失败:', error);
        reject(error);
      };
    });
    
    // 设置iframe源并添加到页面
    testIframe.src = blobUrl;
    document.body.appendChild(testIframe);
    
    try {
      await loadPromise;
      console.log('✅ CSP策略测试通过，iframe可以加载Blob URL');
      
      // 等待一段时间让用户看到测试iframe
      setTimeout(() => {
        document.body.removeChild(testIframe);
        URL.revokeObjectURL(blobUrl);
        console.log('🧹 测试资源已清理');
      }, 3000);
      
    } catch (error) {
      console.error('❌ CSP策略测试失败:', error);
      document.body.removeChild(testIframe);
      URL.revokeObjectURL(blobUrl);
      throw error;
    }
    
    // 6. 测试PDF.js兼容性
    console.log('📄 测试PDF.js兼容性...');
    
    // 检查PDF文件头
    const uint8Array = new Uint8Array(fileBuffer);
    const pdfHeader = String.fromCharCode(...uint8Array.slice(0, 4));
    
    if (pdfHeader === '%PDF') {
      console.log('✅ PDF格式验证通过');
    } else {
      console.warn('⚠️ PDF格式可能有问题，头部:', pdfHeader);
    }
    
    // 7. 模拟实际阅读器使用场景
    console.log('🎭 模拟实际阅读器场景...');
    
    const readerTest = document.createElement('div');
    readerTest.innerHTML = `
      <div style="position: fixed; top: 50px; right: 10px; width: 400px; height: 300px; 
                  border: 2px solid #007bff; background: white; z-index: 10000; padding: 10px;">
        <h3>PDF阅读器测试</h3>
        <iframe src="${blobUrl}" style="width: 100%; height: 250px; border: 1px solid #ddd;"></iframe>
      </div>
    `;
    
    document.body.appendChild(readerTest);
    
    console.log('✅ 模拟阅读器创建成功');
    
    // 清理模拟阅读器
    setTimeout(() => {
      document.body.removeChild(readerTest);
      console.log('🧹 模拟阅读器已清理');
    }, 5000);
    
    console.log('🎉 所有测试完成！');
    console.log('📋 测试结果总结:');
    console.log('  ✅ ElectronAPI功能正常');
    console.log('  ✅ 文件读取功能正常');
    console.log('  ✅ Blob URL创建正常');
    console.log('  ✅ CSP策略配置正确');
    console.log('  ✅ iframe加载Blob URL正常');
    console.log('  ✅ PDF格式验证通过');
    console.log('  ✅ 模拟阅读器功能正常');
    
    return {
      success: true,
      fileSize: fileBuffer.byteLength,
      readDuration: readDuration,
      blobUrl: blobUrl
    };
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// 执行测试
testCSPAndPdfReader().then(result => {
  if (result.success) {
    console.log('🎉 PDF阅读器完整功能测试成功！');
    console.log('现在可以正常使用PDF阅读功能了。');
    console.log('请尝试打开任意PDF图书进行阅读。');
  } else {
    console.error('💥 测试失败:', result.error);
    console.log('请检查错误信息并进行相应修复。');
  }
}).catch(error => {
  console.error('💥 测试执行异常:', error);
});

console.log('📝 测试说明:');
console.log('1. 此测试验证了完整的PDF阅读器功能链路');
console.log('2. 包括文件读取、Blob URL创建、CSP策略、iframe加载等');
console.log('3. 测试过程中会在页面右侧显示测试iframe');
console.log('4. 如果所有测试通过，PDF阅读器应该完全正常工作');
