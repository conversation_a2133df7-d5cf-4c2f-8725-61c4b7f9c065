
// 批量导入图书脚本 - 在浏览器开发者工具中执行
// 生成时间: 2025/8/2 21:17:15

const booksToImport = [
  {
    "path": "D:\\reader\\desktop\\books\\epub\\中国古代建筑艺术.epub",
    "name": "中国古代建筑艺术.epub",
    "size": 7853,
    "format": "epub",
    "directory": "epub",
    "coverPath": null
  },
  {
    "path": "D:\\reader\\desktop\\books\\epub\\人工智能原理与应用.epub",
    "name": "人工智能原理与应用.epub",
    "size": 6938,
    "format": "epub",
    "directory": "epub",
    "coverPath": null
  },
  {
    "path": "D:\\reader\\desktop\\books\\epub\\山海经新解.epub",
    "name": "山海经新解.epub",
    "size": 7824,
    "format": "epub",
    "directory": "epub",
    "coverPath": null
  },
  {
    "path": "D:\\reader\\desktop\\books\\epub\\心理学与生活.epub",
    "name": "心理学与生活.epub",
    "size": 124753710,
    "format": "epub",
    "directory": "epub",
    "coverPath": null
  },
  {
    "path": "D:\\reader\\desktop\\books\\epub\\心理学与生活_processed.epub",
    "name": "心理学与生活_processed.epub",
    "size": 3123055,
    "format": "epub",
    "directory": "epub",
    "coverPath": null
  },
  {
    "path": "D:\\reader\\desktop\\books\\epub\\数据结构与算法.epub",
    "name": "数据结构与算法.epub",
    "size": 6927,
    "format": "epub",
    "directory": "epub",
    "coverPath": null
  },
  {
    "path": "D:\\reader\\desktop\\books\\epub\\春江花月夜.epub",
    "name": "春江花月夜.epub",
    "size": 7842,
    "format": "epub",
    "directory": "epub",
    "coverPath": null
  },
  {
    "path": "D:\\reader\\desktop\\books\\epub\\测试EPUB.epub",
    "name": "测试EPUB.epub",
    "size": 45077201,
    "format": "epub",
    "directory": "epub",
    "coverPath": null
  },
  {
    "path": "D:\\reader\\desktop\\books\\epub\\测试EPUB_processed.epub",
    "name": "测试EPUB_processed.epub",
    "size": 199578,
    "format": "epub",
    "directory": "epub",
    "coverPath": null
  },
  {
    "path": "D:\\reader\\desktop\\books\\epub\\现代JavaScript开发指南.epub",
    "name": "现代JavaScript开发指南.epub",
    "size": 6978,
    "format": "epub",
    "directory": "epub",
    "coverPath": null
  },
  {
    "path": "D:\\reader\\desktop\\books\\epub\\现代诗歌选集.epub",
    "name": "现代诗歌选集.epub",
    "size": 7884,
    "format": "epub",
    "directory": "epub",
    "coverPath": null
  },
  {
    "path": "D:\\reader\\desktop\\books\\epub\\茶道文化.epub",
    "name": "茶道文化.epub",
    "size": 8525,
    "format": "epub",
    "directory": "epub",
    "coverPath": null
  },
  {
    "path": "D:\\reader\\desktop\\books\\epub\\茶道文化_backup.epub",
    "name": "茶道文化_backup.epub",
    "size": 8525,
    "format": "epub",
    "directory": "epub",
    "coverPath": null
  },
  {
    "path": "D:\\reader\\desktop\\books\\epub\\茶道文化_正确内容.txt",
    "name": "茶道文化_正确内容.txt",
    "size": 5002,
    "format": "txt",
    "directory": "epub",
    "coverPath": null
  },
  {
    "path": "D:\\reader\\desktop\\books\\epub\\量子物理学导论.epub",
    "name": "量子物理学导论.epub",
    "size": 6934,
    "format": "epub",
    "directory": "epub",
    "coverPath": null
  },
  {
    "path": "D:\\reader\\desktop\\books\\pdf\\test-document.pdf",
    "name": "test-document.pdf",
    "size": 663,
    "format": "pdf",
    "directory": "pdf",
    "coverPath": null
  },
  {
    "path": "D:\\reader\\desktop\\books\\pdf\\《JavaScript高级编程》.pdf",
    "name": "《JavaScript高级编程》.pdf",
    "size": 5593,
    "format": "pdf",
    "directory": "pdf",
    "coverPath": null
  },
  {
    "path": "D:\\reader\\desktop\\books\\pdf\\《Python编程实战》.pdf",
    "name": "《Python编程实战》.pdf",
    "size": 5470,
    "format": "pdf",
    "directory": "pdf",
    "coverPath": null
  },
  {
    "path": "D:\\reader\\desktop\\books\\pdf\\《世界文明史》.pdf",
    "name": "《世界文明史》.pdf",
    "size": 4796,
    "format": "pdf",
    "directory": "pdf",
    "coverPath": null
  },
  {
    "path": "D:\\reader\\desktop\\books\\pdf\\《中国通史》.pdf",
    "name": "《中国通史》.pdf",
    "size": 4777,
    "format": "pdf",
    "directory": "pdf",
    "coverPath": null
  },
  {
    "path": "D:\\reader\\desktop\\books\\pdf\\《人工智能原理》.pdf",
    "name": "《人工智能原理》.pdf",
    "size": 4712,
    "format": "pdf",
    "directory": "pdf",
    "coverPath": null
  },
  {
    "path": "D:\\reader\\desktop\\books\\pdf\\《哲学思辨录》.pdf",
    "name": "《哲学思辨录》.pdf",
    "size": 4711,
    "format": "pdf",
    "directory": "pdf",
    "coverPath": null
  },
  {
    "path": "D:\\reader\\desktop\\books\\pdf\\《生物学基础》.pdf",
    "name": "《生物学基础》.pdf",
    "size": 4782,
    "format": "pdf",
    "directory": "pdf",
    "coverPath": null
  },
  {
    "path": "D:\\reader\\desktop\\books\\pdf\\《红楼梦》.pdf",
    "name": "《红楼梦》.pdf",
    "size": 38177778,
    "format": "pdf",
    "directory": "pdf",
    "coverPath": null
  },
  {
    "path": "D:\\reader\\desktop\\books\\pdf\\《诗经选读》.pdf",
    "name": "《诗经选读》.pdf",
    "size": 4719,
    "format": "pdf",
    "directory": "pdf",
    "coverPath": null
  },
  {
    "path": "D:\\reader\\desktop\\books\\pdf\\《量子物理学导论》.pdf",
    "name": "《量子物理学导论》.pdf",
    "size": 4779,
    "format": "pdf",
    "directory": "pdf",
    "coverPath": null
  },
  {
    "path": "D:\\reader\\desktop\\books\\txt\\1_人工智能简史.txt",
    "name": "1_人工智能简史.txt",
    "size": 24136,
    "format": "txt",
    "directory": "txt",
    "coverPath": null
  },
  {
    "path": "D:\\reader\\desktop\\books\\txt\\2_量子物理学导论.txt",
    "name": "2_量子物理学导论.txt",
    "size": 3536,
    "format": "txt",
    "directory": "txt",
    "coverPath": null
  },
  {
    "path": "D:\\reader\\desktop\\books\\txt\\3_现代软件工程.txt",
    "name": "3_现代软件工程.txt",
    "size": 2982,
    "format": "txt",
    "directory": "txt",
    "coverPath": null
  },
  {
    "path": "D:\\reader\\desktop\\books\\txt\\4_数据科学实战.txt",
    "name": "4_数据科学实战.txt",
    "size": 2488,
    "format": "txt",
    "directory": "txt",
    "coverPath": null
  },
  {
    "path": "D:\\reader\\desktop\\books\\txt\\5_区块链技术原理.txt",
    "name": "5_区块链技术原理.txt",
    "size": 1212,
    "format": "txt",
    "directory": "txt",
    "coverPath": null
  },
  {
    "path": "D:\\reader\\desktop\\books\\txt\\6_云计算架构设计.txt",
    "name": "6_云计算架构设计.txt",
    "size": 1206,
    "format": "txt",
    "directory": "txt",
    "coverPath": null
  },
  {
    "path": "D:\\reader\\desktop\\books\\txt\\7_网络安全防护.txt",
    "name": "7_网络安全防护.txt",
    "size": 1187,
    "format": "txt",
    "directory": "txt",
    "coverPath": null
  },
  {
    "path": "D:\\reader\\desktop\\books\\txt\\8_移动应用开发.txt",
    "name": "8_移动应用开发.txt",
    "size": 1144,
    "format": "txt",
    "directory": "txt",
    "coverPath": null
  },
  {
    "path": "D:\\reader\\desktop\\books\\txt\\人工智能简史.txt",
    "name": "人工智能简史.txt",
    "size": 1887,
    "format": "txt",
    "directory": "txt",
    "coverPath": null
  },
  {
    "path": "D:\\reader\\desktop\\books\\txt\\数据科学实战.txt",
    "name": "数据科学实战.txt",
    "size": 1749,
    "format": "txt",
    "directory": "txt",
    "coverPath": null
  },
  {
    "path": "D:\\reader\\desktop\\books\\txt\\现代软件工程.txt",
    "name": "现代软件工程.txt",
    "size": 1869,
    "format": "txt",
    "directory": "txt",
    "coverPath": null
  }
];

console.log('📊 准备导入', booksToImport.length, '本图书');

async function batchImportBooks() {
  console.log('🚀 开始批量导入图书...');
  
  let successCount = 0;
  let failCount = 0;
  const results = [];
  
  // 检查导入前状态
  const beforeBooks = await window.electronAPI.book.list();
  console.log(`导入前数据库中有 ${beforeBooks.length} 本图书`);
  
  for (let i = 0; i < booksToImport.length; i++) {
    const book = booksToImport[i];
    console.log(`\n[${i + 1}/${booksToImport.length}] 正在导入: ${book.name}`);
    console.log(`  路径: ${book.path}`);
    console.log(`  格式: ${book.format.toUpperCase()}`);
    console.log(`  大小: ${(book.size / 1024).toFixed(1)} KB`);
    if (book.coverPath) {
      console.log(`  封面: ${book.coverPath}`);
    }
    
    try {
      // 调用导入API
      const result = await window.electronAPI.book.add(book.path);
      
      console.log(`  ✅ 导入成功: ${result.title || book.name}`);
      
      results.push({
        file: book.name,
        success: true,
        result: result,
        message: '导入成功'
      });
      
      successCount++;
      
      // 短暂延迟避免过快导入
      await new Promise(resolve => setTimeout(resolve, 100));
      
    } catch (error) {
      console.error(`  ❌ 导入失败: ${error.message}`);
      
      results.push({
        file: book.name,
        success: false,
        error: error.message,
        message: `导入失败: ${error.message}`
      });
      
      failCount++;
    }
  }
  
  // 检查导入后状态
  const afterBooks = await window.electronAPI.book.list();
  console.log(`\n📊 导入完成统计:`);
  console.log(`  总计: ${booksToImport.length} 本`);
  console.log(`  成功: ${successCount} 本`);
  console.log(`  失败: ${failCount} 本`);
  console.log(`  导入前: ${beforeBooks.length} 本`);
  console.log(`  导入后: ${afterBooks.length} 本`);
  console.log(`  实际新增: ${afterBooks.length - beforeBooks.length} 本`);
  
  // 显示详细结果
  console.log(`\n📋 详细结果:`);
  results.forEach((result, index) => {
    const status = result.success ? '✅' : '❌';
    console.log(`  ${status} ${result.file}: ${result.message}`);
  });
  
  // 显示新导入的图书信息
  if (afterBooks.length > beforeBooks.length) {
    console.log(`\n📚 新导入的图书:`);
    const newBooks = afterBooks.slice(beforeBooks.length);
    newBooks.forEach((book, index) => {
      console.log(`  ${index + 1}. 《${book.title}》 - ${book.author || '未知作者'}`);
      console.log(`     格式: ${book.file_format} | 大小: ${book.file_size} 字节`);
      console.log(`     路径: ${book.file_path}`);
    });
  }
  
  return {
    total: booksToImport.length,
    success: successCount,
    failed: failCount,
    results: results,
    beforeCount: beforeBooks.length,
    afterCount: afterBooks.length
  };
}

// 执行导入
console.log('🎯 执行 batchImportBooks() 开始导入');
batchImportBooks().then(result => {
  console.log('🎉 批量导入完成!', result);
}).catch(error => {
  console.error('💥 批量导入失败:', error);
});
