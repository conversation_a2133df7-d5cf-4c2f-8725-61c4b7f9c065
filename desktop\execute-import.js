/**
 * 直接执行导入脚本
 * 通过 Electron 应用的开发者工具执行批量导入
 */

const fs = require('fs')
const path = require('path')

console.log('🚀 开始执行批量导入...\n')

// 读取生成的导入脚本
const scriptPath = path.join(__dirname, 'import-script.js')

if (!fs.existsSync(scriptPath)) {
  console.error('❌ 导入脚本不存在，请先运行 batch-import-books.js')
  process.exit(1)
}

const scriptContent = fs.readFileSync(scriptPath, 'utf8')

// 提取 booksToImport 数据
const booksMatch = scriptContent.match(/const booksToImport = (\[[\s\S]*?\]);/)
if (!booksMatch) {
  console.error('❌ 无法解析导入脚本中的图书数据')
  process.exit(1)
}

let booksToImport
try {
  booksToImport = JSON.parse(booksMatch[1])
} catch (error) {
  console.error('❌ 解析图书数据失败:', error.message)
  process.exit(1)
}

console.log(`📊 准备导入 ${booksToImport.length} 本图书`)

// 按格式分组统计
const formatStats = {}
booksToImport.forEach(book => {
  formatStats[book.format.toUpperCase()] = (formatStats[book.format.toUpperCase()] || 0) + 1
})

console.log('📋 格式分布:')
Object.entries(formatStats).forEach(([format, count]) => {
  console.log(`  ${format}: ${count} 本`)
})

// 生成浏览器执行脚本
const browserScript = `
// 自动执行批量导入脚本
console.log('🎯 开始自动批量导入图书...');

const booksToImport = ${JSON.stringify(booksToImport, null, 2)};

async function executeBatchImport() {
  console.log('📊 准备导入', booksToImport.length, '本图书');
  
  let successCount = 0;
  let failCount = 0;
  const results = [];
  
  // 检查导入前状态
  try {
    const beforeBooks = await window.electronAPI.book.list();
    console.log(\`导入前数据库中有 \${beforeBooks.length} 本图书\`);
    
    for (let i = 0; i < booksToImport.length; i++) {
      const book = booksToImport[i];
      console.log(\`\\n[\${i + 1}/\${booksToImport.length}] 正在导入: \${book.name}\`);
      console.log(\`  路径: \${book.path}\`);
      console.log(\`  格式: \${book.format.toUpperCase()}\`);
      console.log(\`  大小: \${(book.size / 1024).toFixed(1)} KB\`);
      
      try {
        // 调用导入API
        const result = await window.electronAPI.book.add(book.path);
        
        console.log(\`  ✅ 导入成功: \${result.title || book.name}\`);
        console.log(\`     ID: \${result.id}\`);
        console.log(\`     作者: \${result.author || '未知'}\`);
        console.log(\`     文件格式: \${result.file_format}\`);
        console.log(\`     文件大小: \${result.file_size} 字节\`);
        
        results.push({
          file: book.name,
          success: true,
          result: result,
          message: '导入成功'
        });
        
        successCount++;
        
        // 短暂延迟避免过快导入
        await new Promise(resolve => setTimeout(resolve, 200));
        
      } catch (error) {
        console.error(\`  ❌ 导入失败: \${error.message}\`);
        
        results.push({
          file: book.name,
          success: false,
          error: error.message,
          message: \`导入失败: \${error.message}\`
        });
        
        failCount++;
      }
    }
    
    // 检查导入后状态
    const afterBooks = await window.electronAPI.book.list();
    console.log(\`\\n📊 导入完成统计:\`);
    console.log(\`  总计: \${booksToImport.length} 本\`);
    console.log(\`  成功: \${successCount} 本\`);
    console.log(\`  失败: \${failCount} 本\`);
    console.log(\`  导入前: \${beforeBooks.length} 本\`);
    console.log(\`  导入后: \${afterBooks.length} 本\`);
    console.log(\`  实际新增: \${afterBooks.length - beforeBooks.length} 本\`);
    
    // 显示新导入的图书信息
    if (afterBooks.length > beforeBooks.length) {
      console.log(\`\\n📚 新导入的图书:\`);
      const newBooks = afterBooks.slice(beforeBooks.length);
      newBooks.forEach((book, index) => {
        console.log(\`  \${index + 1}. 《\${book.title}》 - \${book.author || '未知作者'}\`);
        console.log(\`     格式: \${book.file_format} | 大小: \${book.file_size} 字节\`);
        console.log(\`     导入时间: \${book.import_time}\`);
        console.log(\`     文件路径: \${book.file_path}\`);
      });
    }
    
    // 显示失败的文件
    const failedFiles = results.filter(r => !r.success);
    if (failedFiles.length > 0) {
      console.log(\`\\n❌ 导入失败的文件:\`);
      failedFiles.forEach((result, index) => {
        console.log(\`  \${index + 1}. \${result.file}: \${result.message}\`);
      });
    }
    
    console.log(\`\\n🎉 批量导入完成!\`);
    
    return {
      total: booksToImport.length,
      success: successCount,
      failed: failCount,
      results: results,
      beforeCount: beforeBooks.length,
      afterCount: afterBooks.length
    };
    
  } catch (error) {
    console.error('💥 批量导入过程中发生错误:', error);
    throw error;
  }
}

// 立即执行导入
executeBatchImport().then(result => {
  console.log('\\n✅ 批量导入任务完成!', result);
}).catch(error => {
  console.error('\\n💥 批量导入任务失败:', error);
});
`

// 保存浏览器执行脚本
const browserScriptPath = path.join(__dirname, 'browser-import.js')
fs.writeFileSync(browserScriptPath, browserScript, 'utf8')

console.log(`\n✅ 浏览器执行脚本已生成: ${browserScriptPath}`)

// 显示要导入的文件列表（前10个）
console.log(`\n📋 即将导入的文件（前10个）:`)
booksToImport.slice(0, 10).forEach((book, index) => {
  console.log(`  ${index + 1}. ${book.name} (${book.format.toUpperCase()}, ${(book.size / 1024).toFixed(1)} KB)`)
})

if (booksToImport.length > 10) {
  console.log(`  ... 还有 ${booksToImport.length - 10} 个文件`)
}

console.log(`\n🎯 执行方式:`)
console.log(`1. 确保 Electron 应用正在运行 (http://localhost:5173)`)
console.log(`2. 在应用中按 F12 打开开发者工具`)
console.log(`3. 复制并粘贴 browser-import.js 的内容到控制台`)
console.log(`4. 按 Enter 执行，观察导入过程`)

console.log(`\n💡 或者直接复制以下内容到浏览器控制台:`)
console.log(`\n${'='.repeat(50)}`)
console.log(browserScript)
console.log(`${'='.repeat(50)}\n`)
