/**
 * Vue Router 路由配置
 * 定义应用的路由结构和导航规则
 */

import { createRouter, createWebHashHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

// 路由配置
const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/bookshelf'
  },
  // 1. 我的书架模块
  {
    path: '/bookshelf',
    name: 'Bookshelf',
    component: () => import('../views/bookshelf/BookshelfView.vue'),
    meta: {
      title: '我的书架',
      icon: 'Collection',
      group: 'main'
    },
    children: [
      {
        path: '',
        redirect: 'library'
      },
      {
        path: 'library',
        name: 'BookshelfLibrary',
        component: () => import('../views/library/UnifiedLibraryView.vue'),
        meta: {
          title: '图书列表',
          parent: '我的书架'
        }
      },
      {
        path: 'import',
        name: 'BookshelfImport',
        component: () => import('../views/bookshelf/SimpleImportView.vue'),
        meta: {
          title: '导入图书',
          parent: '我的书架'
        }
      },
      {
        path: 'search',
        name: 'BookshelfSearch',
        component: () => import('../views/bookshelf/SearchView.vue'),
        meta: {
          title: '搜索图书',
          parent: '我的书架'
        }
      },
      {
        path: 'reader/:bookId',
        name: 'BookshelfReader',
        component: () => import('../views/reader/UnifiedReader.vue'),
        meta: {
          title: '阅读器',
          parent: '我的书架'
        },
        props: true
      }
    ]
  },
  // 2. 学习服务模块
  {
    path: '/learning',
    name: 'Learning',
    component: () => import('../views/learning/LearningView.vue'),
    meta: {
      title: '学习服务',
      icon: 'Reading',
      group: 'main'
    },
    children: [
      {
        path: '',
        name: 'LearningDefault',
        redirect: '/learning/tasks'
      },
      {
        path: 'tasks',
        name: 'LearningTasks',
        component: () => import('../views/learning/TasksView.vue'),
        meta: {
          title: '学习任务',
          parent: '学习服务'
        }
      },
      {
        path: 'homework',
        name: 'LearningHomework',
        component: () => import('../views/learning/HomeworkView.vue'),
        meta: {
          title: '作业任务',
          parent: '学习服务'
        }
      },
      {
        path: 'tests',
        name: 'LearningTests',
        component: () => import('../views/learning/TestsView.vue'),
        meta: {
          title: '测试任务',
          parent: '学习服务'
        }
      },
      {
        path: 'experiments',
        name: 'LearningExperiments',
        component: () => import('../views/learning/ExperimentsView.vue'),
        meta: {
          title: '实验任务',
          parent: '学习服务'
        }
      }
    ]
  },
  // 3. 书签笔记模块
  {
    path: '/bookmark',
    name: 'Bookmark',
    component: () => import('../views/bookmark/BookmarkView.vue'),
    meta: {
      title: '书签笔记',
      icon: 'Star',
      group: 'main'
    },
    children: [
      {
        path: '',
        name: 'BookmarkDefault',
        redirect: '/bookmark/bookmarks'
      },
      {
        path: 'bookmarks',
        name: 'BookmarkList',
        component: () => import('../views/bookmark/BookmarksView.vue'),
        meta: {
          title: '书签管理',
          parent: '书签笔记'
        }
      },
      {
        path: 'notes',
        name: 'BookmarkNotes',
        component: () => import('../views/bookmark/NotesView.vue'),
        meta: {
          title: '笔记管理',
          parent: '书签笔记'
        }
      },
      {
        path: 'highlights',
        name: 'BookmarkHighlights',
        component: () => import('../views/bookmark/HighlightsView.vue'),
        meta: {
          title: '高亮管理',
          parent: '书签笔记'
        }
      }
    ]
  },
  // 4. 设置模块
  {
    path: '/settings',
    name: 'Settings',
    component: () => import('../views/settings/SettingsView.vue'),
    meta: {
      title: '设置',
      icon: 'Setting',
      group: 'main'
    },
    children: [
      {
        path: '',
        name: 'SettingsDefault',
        redirect: '/settings/general'
      },
      {
        path: 'general',
        name: 'SettingsGeneral',
        component: () => import('../views/settings/GeneralView.vue'),
        meta: {
          title: '通用设置',
          parent: '设置'
        }
      },
      {
        path: 'reading',
        name: 'SettingsReading',
        component: () => import('../views/settings/ReadingView.vue'),
        meta: {
          title: '阅读设置',
          parent: '设置'
        }
      },
      {
        path: 'theme',
        name: 'SettingsTheme',
        component: () => import('../views/settings/ThemeView.vue'),
        meta: {
          title: '主题设置',
          parent: '设置'
        }
      }
    ]
  },
  // 5. 个人中心模块
  {
    path: '/profile',
    name: 'Profile',
    component: () => import('../views/profile/ProfileView.vue'),
    meta: {
      title: '个人中心',
      icon: 'User',
      group: 'main'
    },
    children: [
      {
        path: '',
        name: 'ProfileDefault',
        redirect: '/profile/reports'
      },
      {
        path: 'reports',
        name: 'ProfileReports',
        component: () => import('../views/profile/ReportsView.vue'),
        meta: {
          title: '学习报告',
          parent: '个人中心'
        }
      },
      {
        path: 'account',
        name: 'ProfileAccount',
        component: () => import('../views/profile/AccountView.vue'),
        meta: {
          title: '账号设置',
          parent: '个人中心'
        }
      },
      {
        path: 'network',
        name: 'ProfileNetwork',
        component: () => import('../views/profile/NetworkView.vue'),
        meta: {
          title: '网络设置',
          parent: '个人中心'
        }
      }
    ]
  },

  // 统一图书库页面（独立路由用于测试）
  {
    path: '/unified-library',
    name: 'UnifiedLibrary',
    component: () => import('../views/library/UnifiedLibraryView.vue'),
    meta: {
      title: '统一图书库',
      hideInMenu: false
    }
  },
  // 兼容旧路由（重定向）
  {
    path: '/library',
    redirect: '/bookshelf/library'
  },
  {
    path: '/bookmarks',
    redirect: '/bookmark/bookmarks'
  },
  {
    path: '/notes',
    redirect: '/bookmark/notes'
  },
  // 404页面
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('../views/NotFound.vue'),
    meta: {
      title: '页面未找到',
      hideInMenu: true
    }
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHashHistory(),
  routes
})

// 全局前置守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta?.title) {
    const parentTitle = to.meta?.parent ? `${to.meta.parent} - ` : ''
    document.title = `${parentTitle}${to.meta.title} - Yu Reader`
  }

  // 权限检查（预留）
  // if (to.meta?.requiresAuth && !isAuthenticated()) {
  //   next('/login')
  //   return
  // }

  next()
})

// 全局后置钩子
router.afterEach((to, from) => {
  // 路由切换完成后的处理
  console.log(`路由切换: ${from.path} -> ${to.path}`)

  // 更新页面标题
  if (to.meta?.title) {
    document.title = `${to.meta.title} - Yu Reader`
  }
})

// 路由工具函数
export const getRouteTitle = (route: any) => {
  return route.meta?.title || '未知页面'
}

export const getBreadcrumbs = (route: any) => {
  const breadcrumbs = []

  // 添加父级路由
  if (route.meta?.parent) {
    const parentRoute = routes.find(r => r.meta?.title === route.meta.parent)
    if (parentRoute) {
      breadcrumbs.push({
        title: parentRoute.meta.title,
        path: parentRoute.path,
        icon: parentRoute.meta.icon
      })
    }
  }

  // 添加当前路由
  breadcrumbs.push({
    title: route.meta?.title || '当前页面',
    path: route.path,
    icon: route.meta?.icon,
    current: true
  })

  return breadcrumbs
}

// 获取主导航菜单项
export const getMainMenuItems = () => {
  return routes.filter(route =>
    route.meta?.group === 'main' && !route.meta?.hideInMenu
  ).map(route => ({
    path: route.path,
    name: route.name,
    title: route.meta?.title,
    icon: route.meta?.icon,
    description: route.meta?.description,
    children: route.children?.filter(child => !child.meta?.hideInMenu).map(child => ({
      path: child.path,
      name: child.name,
      title: child.meta?.title,
      icon: child.meta?.icon,
      description: child.meta?.description
    }))
  }))
}

// 检查路由是否激活
export const isRouteActive = (routePath: string, currentPath: string) => {
  return currentPath.startsWith(routePath)
}

// 获取路由层级
export const getRouteLevel = (route: any) => {
  return route.path.split('/').filter(Boolean).length
}

// 查找父级路由
export const findParentRoute = (targetPath: string) => {
  for (const route of routes) {
    if (route.children) {
      for (const child of route.children) {
        if (child.path === targetPath || targetPath.startsWith(child.path)) {
          return route
        }
      }
    }
  }
  return null
}

export default router
