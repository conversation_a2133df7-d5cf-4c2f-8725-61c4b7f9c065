import{d as m,f as g,c as p,g as s,a as l,i,u as o,Z as f,r as u,a9 as y,aa as w,z as h,a8 as d,$ as v,a4 as b,ab as k,ac as x,ad as C,M as V,j as $,H as B,o as N,_ as z}from"./index-BqsStGhU.js";const L={class:"learning-view"},M={class:"learning-content"},R={class:"learning-overview"},T={class:"overview-card"},j={class:"card-icon"},E={class:"overview-card"},H={class:"card-icon"},I={class:"overview-card"},J={class:"card-icon"},S={class:"learning-modules"},Z={class:"module-icon"},q={class:"module-arrow"},A={class:"module-icon"},D={class:"module-arrow"},F={class:"module-icon"},G={class:"module-arrow"},K={class:"module-icon"},O={class:"module-arrow"},P={class:"recent-activities"},Q={class:"activity-list"},U={class:"activity-item"},W={class:"activity-icon"},X={class:"activity-item"},Y={class:"activity-icon"},ss={class:"activity-item"},ts={class:"activity-icon"},ls={class:"learning-suggestions"},is={class:"suggestion-card"},es={class:"suggestion-icon"},os={class:"suggestion-content"},ns=m({__name:"LearningView",setup(ds){const r=B(),n=c=>{r.push(c)};return g(()=>{console.log("学习服务页面已加载")}),(c,t)=>{const e=u("el-icon"),_=u("el-button");return N(),p("div",L,[t[19]||(t[19]=s("div",{class:"learning-header"},[s("h1",{class:"page-title"},"学习服务"),s("p",{class:"page-description"},"智能学习辅助功能，提升您的阅读效率")],-1)),s("div",M,[s("div",R,[s("div",T,[s("div",j,[l(e,null,{default:i(()=>[l(o(f))]),_:1})]),t[4]||(t[4]=s("div",{class:"card-content"},[s("h3",null,"学习进度"),s("div",{class:"progress-info"},[s("div",{class:"progress-number"},"75%"),s("div",{class:"progress-text"},"本周完成度")])],-1))]),s("div",E,[s("div",H,[l(e,null,{default:i(()=>[l(o(y))]),_:1})]),t[5]||(t[5]=s("div",{class:"card-content"},[s("h3",null,"学习时长"),s("div",{class:"progress-info"},[s("div",{class:"progress-number"},"2.5h"),s("div",{class:"progress-text"},"今日阅读")])],-1))]),s("div",I,[s("div",J,[l(e,null,{default:i(()=>[l(o(w))]),_:1})]),t[6]||(t[6]=s("div",{class:"card-content"},[s("h3",null,"学习成就"),s("div",{class:"progress-info"},[s("div",{class:"progress-number"},"12"),s("div",{class:"progress-text"},"获得徽章")])],-1))])]),s("div",S,[s("div",{class:"module-card",onClick:t[0]||(t[0]=a=>n("/learning/tasks"))},[s("div",Z,[l(e,null,{default:i(()=>[l(o(h))]),_:1})]),t[7]||(t[7]=s("div",{class:"module-content"},[s("h3",null,"学习任务"),s("p",null,"查看和管理您的学习计划"),s("div",{class:"module-badge"},"3 个待完成")],-1)),s("div",q,[l(e,null,{default:i(()=>[l(o(d))]),_:1})])]),s("div",{class:"module-card",onClick:t[1]||(t[1]=a=>n("/learning/homework"))},[s("div",A,[l(e,null,{default:i(()=>[l(o(v))]),_:1})]),t[8]||(t[8]=s("div",{class:"module-content"},[s("h3",null,"作业任务"),s("p",null,"完成课后练习和作业"),s("div",{class:"module-badge urgent"},"2 个紧急")],-1)),s("div",D,[l(e,null,{default:i(()=>[l(o(d))]),_:1})])]),s("div",{class:"module-card",onClick:t[2]||(t[2]=a=>n("/learning/tests"))},[s("div",F,[l(e,null,{default:i(()=>[l(o(b))]),_:1})]),t[9]||(t[9]=s("div",{class:"module-content"},[s("h3",null,"测试任务"),s("p",null,"参与在线测试和评估"),s("div",{class:"module-badge"},"1 个进行中")],-1)),s("div",G,[l(e,null,{default:i(()=>[l(o(d))]),_:1})])]),s("div",{class:"module-card",onClick:t[3]||(t[3]=a=>n("/learning/experiments"))},[s("div",K,[l(e,null,{default:i(()=>[l(o(k))]),_:1})]),t[10]||(t[10]=s("div",{class:"module-content"},[s("h3",null,"实验任务"),s("p",null,"进行实践操作和实验"),s("div",{class:"module-badge"},"即将开始")],-1)),s("div",O,[l(e,null,{default:i(()=>[l(o(d))]),_:1})])])]),s("div",P,[t[14]||(t[14]=s("h2",null,"最近活动",-1)),s("div",Q,[s("div",U,[s("div",W,[l(e,null,{default:i(()=>[l(o(x))]),_:1})]),t[11]||(t[11]=s("div",{class:"activity-content"},[s("div",{class:"activity-title"},"完成了《JavaScript高级程序设计》第3章测试"),s("div",{class:"activity-time"},"2小时前")],-1))]),s("div",X,[s("div",Y,[l(e,null,{default:i(()=>[l(o(v))]),_:1})]),t[12]||(t[12]=s("div",{class:"activity-content"},[s("div",{class:"activity-title"},"提交了React组件设计作业"),s("div",{class:"activity-time"},"5小时前")],-1))]),s("div",ss,[s("div",ts,[l(e,null,{default:i(()=>[l(o(C))]),_:1})]),t[13]||(t[13]=s("div",{class:"activity-content"},[s("div",{class:"activity-title"},'获得了"坚持阅读"徽章'),s("div",{class:"activity-time"},"1天前")],-1))])])]),s("div",ls,[t[18]||(t[18]=s("h2",null,"学习建议",-1)),s("div",is,[s("div",es,[l(e,null,{default:i(()=>[l(o(V))]),_:1})]),s("div",os,[t[16]||(t[16]=s("h3",null,"建议复习",-1)),t[17]||(t[17]=s("p",null,'您在"数据结构"章节的测试成绩较低，建议重新阅读相关内容并完成练习。',-1)),l(_,{type:"primary",size:"small"},{default:i(()=>t[15]||(t[15]=[$("开始复习")])),_:1,__:[15]})])])])])])}}}),cs=z(ns,[["__scopeId","data-v-90c12236"]]);export{cs as default};
