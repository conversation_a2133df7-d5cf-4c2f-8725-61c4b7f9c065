/**
 * 测试API调用脚本
 * 在浏览器开发者工具中运行此脚本来测试图书数据加载
 */

console.log('🧪 开始测试图书API调用...')

async function testBookAPI() {
  try {
    console.log('📚 测试获取图书列表...')
    
    // 检查electronAPI是否可用
    if (!window.electronAPI) {
      console.error('❌ window.electronAPI 不可用')
      return
    }
    
    if (!window.electronAPI.book) {
      console.error('❌ window.electronAPI.book 不可用')
      return
    }
    
    console.log('✅ electronAPI 可用，开始调用...')
    
    // 调用API
    const books = await window.electronAPI.book.list()
    
    console.log(`✅ 成功获取 ${books.length} 本图书:`)
    
    if (books.length > 0) {
      books.forEach((book, index) => {
        console.log(`  ${index + 1}. ${book.title} - ${book.author}`)
        console.log(`     ID: ${book.id}`)
        console.log(`     文件: ${book.filePath}`)
        console.log(`     格式: ${book.format}`)
        console.log(`     大小: ${book.fileSize} 字节`)
        console.log(`     阅读状态: ${book.readingStatus}`)
        console.log(`     进度: ${book.readingProgressPercent}%`)
        console.log('')
      })
    } else {
      console.log('📝 数据库中没有图书数据')
    }
    
    return books
    
  } catch (error) {
    console.error('❌ API调用失败:', error)
    throw error
  }
}

// 立即执行测试
testBookAPI()

// 额外的调试函数
window.debugBookAPI = testBookAPI

// 检查Vue应用状态
window.checkVueApp = function() {
  console.log('🔍 检查Vue应用状态...')

  // 检查是否有Vue应用实例
  const app = document.querySelector('#app').__vue_app__
  if (app) {
    console.log('✅ Vue应用实例存在')

    // 检查Pinia store
    const stores = app.config.globalProperties.$pinia._s
    console.log('📦 Pinia stores:', Array.from(stores.keys()))

    // 检查unifiedLibrary store
    if (stores.has('unifiedLibrary')) {
      const store = stores.get('unifiedLibrary')
      console.log('📚 UnifiedLibrary store状态:')
      console.log('  - books数量:', store.books.length)
      console.log('  - loading:', store.loading)
      console.log('  - error:', store.error)
      console.log('  - books数据:', store.books.slice(0, 2))
    } else {
      console.log('❌ UnifiedLibrary store不存在')
    }
  } else {
    console.log('❌ Vue应用实例不存在')
  }
}
