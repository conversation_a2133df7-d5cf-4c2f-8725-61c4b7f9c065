import{N as ne,b as c,e as R,d as ue,w as le,f as ie,c as P,g as o,a as n,i as d,r as h,j as m,u as Q,O as de,l as se,P as ce,Q as ve,t as q,F as oe,n as re,h as fe,q as ge,H as pe,o as k,_ as _e}from"./index-BqsStGhU.js";const he=ne("bookshelf",()=>{const p=c([]),x=c(!1),b=c(null),U=c("grid"),F=c("addedAt"),w=c("desc"),g=c(20),_=c(1),u=c({search:"",author:"",formats:[],category:"",isFavorite:void 0,language:"",readingStatus:"all",tags:[],dateRange:{}}),r=c(new Set),V=c(!1),T=c(null),D=c(!1),A=c([]),N=c([]),O=c([]),y=R(()=>{let e=[...p.value||[]];if(u.value.search){const a=u.value.search.toLowerCase();e=e.filter(s=>s.title.toLowerCase().includes(a)||s.author.toLowerCase().includes(a)||s.description&&s.description.toLowerCase().includes(a))}if(u.value.author&&(e=e.filter(a=>a.author.toLowerCase().includes(u.value.author.toLowerCase()))),u.value.formats.length>0&&(e=e.filter(a=>u.value.formats.includes(a.format))),u.value.category&&(e=e.filter(a=>a.category===u.value.category)),u.value.isFavorite!==void 0&&(e=e.filter(a=>a.isFavorite===u.value.isFavorite)),u.value.language&&(e=e.filter(a=>a.language===u.value.language)),u.value.readingStatus!=="all")switch(u.value.readingStatus){case"unread":e=e.filter(a=>a.readProgress===0);break;case"reading":e=e.filter(a=>a.readProgress>0&&a.readProgress<100);break;case"finished":e=e.filter(a=>a.readProgress===100);break}return u.value.tags.length>0&&(e=e.filter(a=>{if(!a.tags)return!1;try{const s=JSON.parse(a.tags);return u.value.tags.some(f=>s.includes(f))}catch{return!1}})),u.value.dateRange.start&&(e=e.filter(a=>a.addedAt>=u.value.dateRange.start)),u.value.dateRange.end&&(e=e.filter(a=>a.addedAt<=u.value.dateRange.end)),e}),$=R(()=>{const e=[...y.value];return e.sort((a,s)=>{let f,S;switch(F.value){case"title":f=a.title.toLowerCase(),S=s.title.toLowerCase();break;case"author":f=a.author.toLowerCase(),S=s.author.toLowerCase();break;case"addedAt":f=new Date(a.addedAt).getTime(),S=new Date(s.addedAt).getTime();break;case"lastReadAt":f=a.lastReadAt?new Date(a.lastReadAt).getTime():0,S=s.lastReadAt?new Date(s.lastReadAt).getTime():0;break;case"readProgress":f=a.readProgress,S=s.readProgress;break;case"fileSize":f=a.fileSize,S=s.fileSize;break;default:f=a.addedAt,S=s.addedAt}return f<S?w.value==="asc"?-1:1:f>S?w.value==="asc"?1:-1:0}),e}),I=R(()=>{const e=(_.value-1)*g.value,a=e+g.value;return $.value.slice(e,a)}),H=R(()=>{const e=p.value||[],a=y.value||[];return{total:e.length,filtered:a.length,unread:e.filter(s=>s.readProgress===0).length,reading:e.filter(s=>s.readProgress>0&&s.readProgress<100).length,finished:e.filter(s=>s.readProgress===100).length,favorites:e.filter(s=>s.isFavorite).length,selected:r.value.size}}),j=R(()=>({current:_.value,pageSize:g.value,total:y.value.length,totalPages:Math.ceil(y.value.length/g.value)})),G=async(e=!1)=>{if(!(x.value&&!e))try{x.value=!0,b.value=null,p.value=[]}catch(a){b.value=a instanceof Error?a.message:"加载图书列表失败",console.error("加载图书列表失败:",a)}finally{x.value=!1}},l=async e=>{u.value.search=e,_.value=1},t=e=>{Object.assign(u.value,e),_.value=1},v=()=>{u.value={search:"",author:"",formats:[],category:"",isFavorite:void 0,language:"",readingStatus:"all",tags:[],dateRange:{}},_.value=1},B=(e,a)=>{F.value===e&&!a?w.value=w.value==="asc"?"desc":"asc":(F.value=e,w.value=a||"asc"),_.value=1},K=e=>{U.value=e},L=e=>{g.value=e,_.value=1},X=e=>{e>=1&&e<=j.value.totalPages&&(_.value=e)},z=e=>{r.value.has(e)?r.value.delete(e):r.value.add(e)},W=()=>{r.value.size===I.value.length?r.value.clear():(r.value.clear(),I.value.forEach(e=>r.value.add(e.id)))},Y=()=>{r.value.clear(),V.value=!1},Z=()=>{V.value=!V.value,V.value||r.value.clear()},C=e=>{p.value.unshift(e)},M=(e,a)=>{const s=p.value.findIndex(f=>f.id===e);s!==-1&&(p.value[s]={...p.value[s],...a})},ee=e=>{const a=p.value.findIndex(s=>s.id===e);a!==-1&&p.value.splice(a,1),r.value.delete(e)},te=e=>{p.value=p.value.filter(a=>!e.includes(a.id)),e.forEach(a=>r.value.delete(a))},J=async(e,a)=>{try{M(e,{isFavorite:a})}catch(s){throw console.error("设置收藏状态失败:",s),s}},i=async(e,a)=>{try{e.forEach(s=>M(s,{isFavorite:a}))}catch(s){throw console.error("批量设置收藏状态失败:",s),s}},E=async(e,a,s)=>{try{const f={readProgress:a,lastReadAt:new Date().toISOString()};s!==void 0&&(f.currentPage=s),M(e,f)}catch(f){throw console.error("更新阅读进度失败:",f),f}},ae=async()=>{try{A.value=[],N.value=[],O.value=[]}catch(e){console.error("加载缓存数据失败:",e)}};return{books:p,loading:x,error:b,viewMode:U,sortField:F,sortOrder:w,pageSize:g,currentPage:_,filter:u,selectedBooks:r,isSelectionMode:V,importProgress:T,isImporting:D,categories:A,tags:N,authors:O,filteredBooks:y,sortedBooks:$,paginatedBooks:I,stats:H,pagination:j,loadBooks:G,searchBooks:l,setFilter:t,clearFilter:v,setSort:B,setViewMode:K,setPageSize:L,goToPage:X,toggleBookSelection:z,toggleSelectAll:W,clearSelection:Y,toggleSelectionMode:Z,addBook:C,updateBook:M,removeBook:ee,removeBooks:te,setFavorite:J,setBatchFavorite:i,updateProgress:E,loadCacheData:ae,startImport:()=>{D.value=!0,T.value={total:0,completed:0,current:"",percentage:0,status:"pending",errors:[]}},updateImportProgress:e=>{T.value=e},finishImport:e=>{D.value=!1,e.imported.forEach(a=>C(a)),ae()}}}),me={class:"search-view"},ye={class:"search-content"},Se={class:"search-box"},ke={class:"advanced-search"},we={class:"search-filters"},Te={class:"filter-group"},Be={class:"filter-group"},Ce={class:"filter-group"},Pe={class:"filter-actions"},Ve={class:"search-results"},Re={class:"results-header"},xe={class:"results-count"},Fe={key:0,class:"books-grid"},Ae=["onClick"],Le={class:"book-cover"},ze=["src","alt"],Me={class:"book-info"},be=["innerHTML"],De=["innerHTML"],Ie={class:"book-meta"},Ee={class:"file-type"},qe={class:"file-size"},Ue={key:0,class:"book-progress"},Ne={key:1,class:"no-results"},Oe={class:"no-results-icon"},$e={key:2,class:"search-tips"},He={class:"tips-icon"},je={class:"quick-searches"},Je={class:"quick-tags"},Qe=ue({__name:"SearchView",setup(p){const x=pe(),b=he(),{books:U,searchBooks:F,setFilter:w}=b,g=c(""),_=c(!1),u=c("relevance"),r=c({fileTypes:[],readStatus:"all",sizeRange:[0,100]}),V=c(["小说","技术","历史","科幻","传记","JavaScript","Python","Vue","React"]),T=R(()=>{let l=U.value||[];if(g.value){const t=g.value.toLowerCase();l=l.filter(v=>v.title.toLowerCase().includes(t)||v.author.toLowerCase().includes(t))}return r.value.fileTypes&&r.value.fileTypes.length>0&&(l=l.filter(t=>r.value.fileTypes.includes(t.fileType))),r.value.readStatus!=="all"&&(l=l.filter(t=>{switch(r.value.readStatus){case"unread":return t.progress===0;case"reading":return t.progress>0&&t.progress<100;case"finished":return t.progress===100;default:return!0}})),l}),D=R(()=>{const l=[...T.value||[]];switch(u.value){case"title":return l.sort((t,v)=>t.title.localeCompare(v.title));case"author":return l.sort((t,v)=>t.author.localeCompare(v.author));case"addTime":return l.sort((t,v)=>v.addTime-t.addTime);case"lastRead":return l.sort((t,v)=>v.lastRead-t.lastRead);default:return l}}),A=()=>{F(g.value)},N=()=>{_.value=!_.value},O=()=>{r.value={fileTypes:[],readStatus:"all",sizeRange:[0,100]},y()},y=()=>{const l={};r.value.fileTypes&&r.value.fileTypes.length>0&&(l.formats=r.value.fileTypes),r.value.readStatus!=="all"&&(l.readingStatus=r.value.readStatus),w(l)},$=l=>{g.value=l,A()},I=l=>{x.push(`/reader/${l.id}`)};le(g,l=>{l.trim()&&A()},{debounce:300}),le(r,()=>{y()},{deep:!0});const H=l=>{if(!g.value)return l;const t=new RegExp(`(${g.value})`,"gi");return l.replace(t,"<mark>$1</mark>")},j=l=>{if(!l)return"0 B";const t=1024,v=["B","KB","MB","GB"],B=Math.floor(Math.log(l)/Math.log(t));return parseFloat((l/Math.pow(t,B)).toFixed(1))+" "+v[B]},G=l=>`${l} MB`;return ie(()=>{console.log("搜索图书页面已加载")}),(l,t)=>{var J;const v=h("el-icon"),B=h("el-button"),K=h("el-input"),L=h("el-checkbox"),X=h("el-checkbox-group"),z=h("el-radio"),W=h("el-radio-group"),Y=h("el-slider"),Z=h("el-collapse-transition"),C=h("el-option"),M=h("el-select"),ee=h("el-progress"),te=h("el-tag");return k(),P("div",me,[t[24]||(t[24]=o("div",{class:"search-header"},[o("h1",{class:"page-title"},"搜索图书"),o("p",{class:"page-description"},"快速查找您的图书收藏")],-1)),o("div",ye,[o("div",Se,[n(K,{modelValue:g.value,"onUpdate:modelValue":t[0]||(t[0]=i=>g.value=i),placeholder:"输入书名、作者或关键词...",size:"large",clearable:"",onInput:A},{prefix:d(()=>[n(v,null,{default:d(()=>[n(Q(se))]),_:1})]),append:d(()=>[n(B,{onClick:N},{default:d(()=>[n(v,null,{default:d(()=>[n(Q(de))]),_:1}),t[5]||(t[5]=m(" 高级搜索 "))]),_:1,__:[5]})]),_:1},8,["modelValue"])]),n(Z,null,{default:d(()=>[ce(o("div",ke,[o("div",we,[o("div",Te,[t[10]||(t[10]=o("label",null,"文件格式",-1)),n(X,{modelValue:r.value.fileTypes,"onUpdate:modelValue":t[1]||(t[1]=i=>r.value.fileTypes=i)},{default:d(()=>[n(L,{value:"epub"},{default:d(()=>t[6]||(t[6]=[m("EPUB")])),_:1,__:[6]}),n(L,{value:"pdf"},{default:d(()=>t[7]||(t[7]=[m("PDF")])),_:1,__:[7]}),n(L,{value:"mobi"},{default:d(()=>t[8]||(t[8]=[m("MOBI")])),_:1,__:[8]}),n(L,{value:"txt"},{default:d(()=>t[9]||(t[9]=[m("TXT")])),_:1,__:[9]})]),_:1},8,["modelValue"])]),o("div",Be,[t[15]||(t[15]=o("label",null,"阅读状态",-1)),n(W,{modelValue:r.value.readStatus,"onUpdate:modelValue":t[2]||(t[2]=i=>r.value.readStatus=i)},{default:d(()=>[n(z,{value:"all"},{default:d(()=>t[11]||(t[11]=[m("全部")])),_:1,__:[11]}),n(z,{value:"unread"},{default:d(()=>t[12]||(t[12]=[m("未读")])),_:1,__:[12]}),n(z,{value:"reading"},{default:d(()=>t[13]||(t[13]=[m("在读")])),_:1,__:[13]}),n(z,{value:"finished"},{default:d(()=>t[14]||(t[14]=[m("已读")])),_:1,__:[14]})]),_:1},8,["modelValue"])]),o("div",Ce,[t[16]||(t[16]=o("label",null,"文件大小",-1)),n(Y,{modelValue:r.value.sizeRange,"onUpdate:modelValue":t[3]||(t[3]=i=>r.value.sizeRange=i),range:"",min:0,max:100,"format-tooltip":G},null,8,["modelValue"])]),o("div",Pe,[n(B,{onClick:O},{default:d(()=>t[17]||(t[17]=[m("重置")])),_:1,__:[17]}),n(B,{type:"primary",onClick:y},{default:d(()=>t[18]||(t[18]=[m("应用筛选")])),_:1,__:[18]})])])],512),[[ve,_.value]])]),_:1}),o("div",Ve,[o("div",Re,[o("span",xe," 找到 "+q(((J=T.value)==null?void 0:J.length)||0)+" 本图书 ",1),n(M,{modelValue:u.value,"onUpdate:modelValue":t[4]||(t[4]=i=>u.value=i),placeholder:"排序方式",style:{width:"150px"}},{default:d(()=>[n(C,{label:"相关性",value:"relevance"}),n(C,{label:"书名",value:"title"}),n(C,{label:"作者",value:"author"}),n(C,{label:"添加时间",value:"addTime"}),n(C,{label:"最后阅读",value:"lastRead"})]),_:1},8,["modelValue"])]),T.value&&T.value.length>0?(k(),P("div",Fe,[(k(!0),P(oe,null,re(D.value,i=>{var E;return k(),P("div",{key:i.id,class:"book-card",onClick:ae=>I(i)},[o("div",Le,[o("img",{src:i.cover||"/placeholder-book.png",alt:i.title},null,8,ze)]),o("div",Me,[o("h3",{innerHTML:H(i.title)},null,8,be),o("p",{innerHTML:H(i.author)},null,8,De),o("div",Ie,[o("span",Ee,q((E=i.fileType)==null?void 0:E.toUpperCase()),1),o("span",qe,q(j(i.fileSize)),1)]),i.progress>0?(k(),P("div",Ue,[n(ee,{percentage:i.progress,"show-text":!1},null,8,["percentage"]),o("span",null,q(i.progress)+"%",1)])):fe("",!0)])],8,Ae)}),128))])):g.value?(k(),P("div",Ne,[o("div",Oe,[n(v,null,{default:d(()=>[n(Q(se))]),_:1})]),t[19]||(t[19]=o("h3",null,"未找到相关图书",-1)),t[20]||(t[20]=o("p",null,"尝试使用不同的关键词或调整筛选条件",-1))])):(k(),P("div",$e,[o("div",He,[n(v,null,{default:d(()=>[n(Q(se))]),_:1})]),t[22]||(t[22]=o("h3",null,"开始搜索",-1)),t[23]||(t[23]=o("p",null,"输入书名、作者或关键词来查找图书",-1)),o("div",je,[t[21]||(t[21]=o("h4",null,"快速搜索",-1)),o("div",Je,[(k(!0),P(oe,null,re(V.value||[],i=>(k(),ge(te,{key:i,onClick:E=>$(i),class:"quick-tag"},{default:d(()=>[m(q(i),1)]),_:2},1032,["onClick"]))),128))])])]))])])])}}}),Ye=_e(Qe,[["__scopeId","data-v-e8df71a1"]]);export{Ye as default};
