import{d as t,f as a,c as n,g as e,a as c,r,o as p,_}from"./index-BqsStGhU.js";const d={class:"homework-view"},i={class:"homework-content"},l=t({__name:"HomeworkView",setup(m){return a(()=>{console.log("作业任务页面已加载")}),(f,o)=>{const s=r("el-empty");return p(),n("div",d,[o[0]||(o[0]=e("div",{class:"page-header"},[e("h1",{class:"page-title"},"作业任务"),e("p",{class:"page-description"},"完成课后练习和作业")],-1)),e("div",i,[c(s,{description:"作业任务功能开发中..."})])])}}}),k=_(l,[["__scopeId","data-v-f31a5c69"]]);export{k as default};
