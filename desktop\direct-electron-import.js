/**
 * 直接通过 Electron 进程间通信执行导入
 * 使用 WebSocket 或其他方式与运行中的 Electron 应用通信
 */

const fs = require('fs')
const path = require('path')
const net = require('net')

console.log('🚀 直接 Electron 导入执行器...\n')

// 读取要导入的文件列表
const scriptPath = path.join(__dirname, 'console-import-script.js')
const scriptContent = fs.readFileSync(scriptPath, 'utf8')
const filesMatch = scriptContent.match(/const filesToImport = (\[[\s\S]*?\]);/)
const filesToImport = JSON.parse(filesMatch[1])

console.log(`📊 准备导入 ${filesToImport.length} 本图书`)

// 按格式统计
const formatStats = {}
filesToImport.forEach(filePath => {
  const ext = path.extname(filePath).toLowerCase().substring(1)
  formatStats[ext.toUpperCase()] = (formatStats[ext.toUpperCase()] || 0) + 1
})

console.log('📋 格式分布:')
Object.entries(formatStats).forEach(([format, count]) => {
  console.log(`  ${format}: ${count} 本`)
})

// 显示文件列表（前15个）
console.log(`\n📚 即将导入的图书（前15个）:`)
filesToImport.slice(0, 15).forEach((filePath, index) => {
  const fileName = path.basename(filePath)
  const fileSize = fs.existsSync(filePath) ? fs.statSync(filePath).size : 0
  const format = path.extname(filePath).toUpperCase().substring(1)
  console.log(`  ${index + 1}. ${fileName} (${format}, ${(fileSize / 1024).toFixed(1)} KB)`)
})

if (filesToImport.length > 15) {
  console.log(`  ... 还有 ${filesToImport.length - 15} 个文件`)
}

// 创建完整的导入脚本
const fullImportScript = `
console.log('🎯 开始系统批量导入 ${filesToImport.length} 本图书...');

const filesToImport = ${JSON.stringify(filesToImport, null, 2)};

async function systemBatchImport() {
  console.log('📊 导入统计:');
  console.log('  EPUB: ${formatStats.EPUB || 0} 本');
  console.log('  TXT: ${formatStats.TXT || 0} 本');
  console.log('  PDF: ${formatStats.PDF || 0} 本');
  console.log('');
  
  let successCount = 0;
  let failCount = 0;
  const results = [];
  const startTime = Date.now();
  
  // 检查导入前状态
  console.log('🔍 检查导入前数据库状态...');
  const beforeBooks = await window.electronAPI.book.list();
  console.log(\`导入前数据库中有 \${beforeBooks.length} 本图书\`);
  
  console.log('\\n🚀 开始批量导入过程...');
  
  for (let i = 0; i < filesToImport.length; i++) {
    const filePath = filesToImport[i];
    const fileName = filePath.split(/[\\\\\\/]/).pop();
    const fileFormat = fileName.split('.').pop().toUpperCase();
    
    console.log(\`\\n[\${i + 1}/\${filesToImport.length}] 正在导入: \${fileName}\`);
    console.log(\`  📁 路径: \${filePath}\`);
    console.log(\`  📄 格式: \${fileFormat}\`);
    
    try {
      // 检查文件是否存在
      const fileExists = await window.electronAPI.file.exists(filePath);
      if (!fileExists) {
        throw new Error('文件不存在');
      }
      
      // 调用导入API
      const result = await window.electronAPI.book.add(filePath);
      
      console.log(\`  ✅ 导入成功: 《\${result.title || fileName}》\`);
      console.log(\`     📖 ID: \${result.id}\`);
      console.log(\`     👤 作者: \${result.author || '未知作者'}\`);
      console.log(\`     📊 格式: \${result.file_format}\`);
      console.log(\`     💾 大小: \${result.file_size} 字节 (\${(result.file_size / 1024).toFixed(1)} KB)\`);
      console.log(\`     📅 导入时间: \${result.import_time}\`);
      
      results.push({
        index: i + 1,
        file: fileName,
        success: true,
        result: result,
        message: '导入成功',
        title: result.title,
        author: result.author,
        format: result.file_format,
        size: result.file_size
      });
      
      successCount++;
      
      // 显示进度
      const progress = ((i + 1) / filesToImport.length * 100).toFixed(1);
      console.log(\`  📈 进度: \${progress}% (\${i + 1}/\${filesToImport.length})\`);
      
      // 短暂延迟避免过快导入
      await new Promise(resolve => setTimeout(resolve, 150));
      
    } catch (error) {
      console.error(\`  ❌ 导入失败: \${error.message}\`);
      
      results.push({
        index: i + 1,
        file: fileName,
        success: false,
        error: error.message,
        message: \`导入失败: \${error.message}\`
      });
      
      failCount++;
    }
  }
  
  const endTime = Date.now();
  const duration = ((endTime - startTime) / 1000).toFixed(1);
  
  // 检查导入后状态
  console.log('\\n🔍 检查导入后数据库状态...');
  const afterBooks = await window.electronAPI.book.list();
  
  console.log(\`\\n📊 批量导入完成统计:\`);
  console.log(\`  ⏱️  总耗时: \${duration} 秒\`);
  console.log(\`  📚 总计: \${filesToImport.length} 本\`);
  console.log(\`  ✅ 成功: \${successCount} 本\`);
  console.log(\`  ❌ 失败: \${failCount} 本\`);
  console.log(\`  📈 成功率: \${(successCount / filesToImport.length * 100).toFixed(1)}%\`);
  console.log(\`  🗄️  导入前: \${beforeBooks.length} 本\`);
  console.log(\`  🗄️  导入后: \${afterBooks.length} 本\`);
  console.log(\`  ➕ 实际新增: \${afterBooks.length - beforeBooks.length} 本\`);
  
  // 显示成功导入的图书
  const successResults = results.filter(r => r.success);
  if (successResults.length > 0) {
    console.log(\`\\n📚 成功导入的图书列表:\`);
    successResults.forEach((result, index) => {
      console.log(\`  \${index + 1}. 《\${result.title || result.file}》 - \${result.author || '未知作者'}\`);
      console.log(\`     格式: \${result.format} | 大小: \${(result.size / 1024).toFixed(1)} KB\`);
    });
  }
  
  // 显示失败的文件
  const failedResults = results.filter(r => !r.success);
  if (failedResults.length > 0) {
    console.log(\`\\n❌ 导入失败的文件:\`);
    failedResults.forEach((result, index) => {
      console.log(\`  \${index + 1}. \${result.file}: \${result.message}\`);
    });
  }
  
  console.log(\`\\n🎉 系统批量导入任务完成!\`);
  console.log(\`📋 详细结果已记录在控制台中\`);
  
  return {
    total: filesToImport.length,
    success: successCount,
    failed: failCount,
    duration: duration,
    beforeCount: beforeBooks.length,
    afterCount: afterBooks.length,
    results: results
  };
}

// 立即执行导入
systemBatchImport().then(result => {
  console.log('\\n✅ 系统批量导入任务完成!', result);
  
  // 可选：刷新页面以显示新导入的图书
  if (result.success > 0) {
    console.log('\\n🔄 建议刷新页面以查看新导入的图书');
  }
  
}).catch(error => {
  console.error('\\n💥 系统批量导入任务失败:', error);
});
`

// 保存完整脚本
const fullScriptPath = path.join(__dirname, 'full-import-script.js')
fs.writeFileSync(fullScriptPath, fullImportScript, 'utf8')

console.log(`\n✅ 完整导入脚本已生成: ${fullScriptPath}`)

console.log(`\n🎯 执行步骤:`)
console.log(`1. 确保 Electron 应用正在运行 (已检测到运行中)`)
console.log(`2. 在应用中按 F12 打开开发者工具`)
console.log(`3. 切换到 Console 标签页`)
console.log(`4. 复制以下完整脚本内容:`)

console.log(`\n${'='.repeat(100)}`)
console.log(fullImportScript)
console.log(`${'='.repeat(100)}`)

console.log(`\n5. 粘贴到控制台并按 Enter 执行`)
console.log(`6. 观察详细的导入过程和统计信息`)
console.log(`7. 导入完成后在图书列表中查看结果`)

console.log(`\n💡 重要提示:`)
console.log(`- 导入过程预计需要 ${Math.ceil(filesToImport.length * 0.2)} 秒左右`)
console.log(`- 请保持开发者工具打开以查看详细日志`)
console.log(`- 导入完成后会显示详细的统计信息`)
console.log(`- 如有失败的文件，会显示具体的错误原因`)

console.log(`\n🚀 现在请在 Electron 应用中执行上述脚本！`)
