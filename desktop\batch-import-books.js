/**
 * 批量导入图书脚本
 * 系统性地扫描 D:\reader\desktop\books 文件夹并导入所有电子书文件
 */

const fs = require('fs')
const path = require('path')

console.log('📚 开始批量导入图书...\n')

// 支持的文件格式
const supportedFormats = ['.txt', '.epub', '.pdf', '.mobi', '.azw3']

// 扫描目录
const booksDir = path.join(__dirname, 'books')

/**
 * 递归扫描目录获取所有电子书文件
 */
function scanDirectory(dir) {
  const files = []
  
  try {
    const items = fs.readdirSync(dir)
    
    for (const item of items) {
      const fullPath = path.join(dir, item)
      const stat = fs.statSync(fullPath)
      
      if (stat.isDirectory()) {
        // 递归扫描子目录
        files.push(...scanDirectory(fullPath))
      } else if (stat.isFile()) {
        const ext = path.extname(item).toLowerCase()
        if (supportedFormats.includes(ext)) {
          files.push({
            path: fullPath,
            name: item,
            size: stat.size,
            format: ext.substring(1), // 移除点号
            directory: path.relative(booksDir, dir) || 'root'
          })
        }
      }
    }
  } catch (error) {
    console.error(`❌ 扫描目录失败 ${dir}:`, error.message)
  }
  
  return files
}

/**
 * 检查封面文件
 */
function findCoverFile(bookPath) {
  const bookDir = path.dirname(bookPath)
  const bookName = path.basename(bookPath, path.extname(bookPath))
  
  // 可能的封面文件名
  const coverNames = [
    `${bookName}.jpg`,
    `${bookName}.jpeg`,
    `${bookName}.png`,
    `${bookName}.webp`,
    'cover.jpg',
    'cover.jpeg',
    'cover.png',
    'cover.webp'
  ]
  
  for (const coverName of coverNames) {
    const coverPath = path.join(bookDir, coverName)
    if (fs.existsSync(coverPath)) {
      return coverPath
    }
  }
  
  return null
}

/**
 * 生成导入脚本
 */
function generateImportScript(files) {
  const importData = files.map(file => ({
    path: file.path,
    name: file.name,
    size: file.size,
    format: file.format,
    directory: file.directory,
    coverPath: findCoverFile(file.path)
  }))

  return `
// 批量导入图书脚本 - 在浏览器开发者工具中执行
// 生成时间: ${new Date().toLocaleString()}

const booksToImport = ${JSON.stringify(importData, null, 2)};

console.log('📊 准备导入', booksToImport.length, '本图书');

async function batchImportBooks() {
  console.log('🚀 开始批量导入图书...');
  
  let successCount = 0;
  let failCount = 0;
  const results = [];
  
  // 检查导入前状态
  const beforeBooks = await window.electronAPI.book.list();
  console.log(\`导入前数据库中有 \${beforeBooks.length} 本图书\`);
  
  for (let i = 0; i < booksToImport.length; i++) {
    const book = booksToImport[i];
    console.log(\`\\n[\${i + 1}/\${booksToImport.length}] 正在导入: \${book.name}\`);
    console.log(\`  路径: \${book.path}\`);
    console.log(\`  格式: \${book.format.toUpperCase()}\`);
    console.log(\`  大小: \${(book.size / 1024).toFixed(1)} KB\`);
    if (book.coverPath) {
      console.log(\`  封面: \${book.coverPath}\`);
    }
    
    try {
      // 调用导入API
      const result = await window.electronAPI.book.add(book.path);
      
      console.log(\`  ✅ 导入成功: \${result.title || book.name}\`);
      
      results.push({
        file: book.name,
        success: true,
        result: result,
        message: '导入成功'
      });
      
      successCount++;
      
      // 短暂延迟避免过快导入
      await new Promise(resolve => setTimeout(resolve, 100));
      
    } catch (error) {
      console.error(\`  ❌ 导入失败: \${error.message}\`);
      
      results.push({
        file: book.name,
        success: false,
        error: error.message,
        message: \`导入失败: \${error.message}\`
      });
      
      failCount++;
    }
  }
  
  // 检查导入后状态
  const afterBooks = await window.electronAPI.book.list();
  console.log(\`\\n📊 导入完成统计:\`);
  console.log(\`  总计: \${booksToImport.length} 本\`);
  console.log(\`  成功: \${successCount} 本\`);
  console.log(\`  失败: \${failCount} 本\`);
  console.log(\`  导入前: \${beforeBooks.length} 本\`);
  console.log(\`  导入后: \${afterBooks.length} 本\`);
  console.log(\`  实际新增: \${afterBooks.length - beforeBooks.length} 本\`);
  
  // 显示详细结果
  console.log(\`\\n📋 详细结果:\`);
  results.forEach((result, index) => {
    const status = result.success ? '✅' : '❌';
    console.log(\`  \${status} \${result.file}: \${result.message}\`);
  });
  
  // 显示新导入的图书信息
  if (afterBooks.length > beforeBooks.length) {
    console.log(\`\\n📚 新导入的图书:\`);
    const newBooks = afterBooks.slice(beforeBooks.length);
    newBooks.forEach((book, index) => {
      console.log(\`  \${index + 1}. 《\${book.title}》 - \${book.author || '未知作者'}\`);
      console.log(\`     格式: \${book.file_format} | 大小: \${book.file_size} 字节\`);
      console.log(\`     路径: \${book.file_path}\`);
    });
  }
  
  return {
    total: booksToImport.length,
    success: successCount,
    failed: failCount,
    results: results,
    beforeCount: beforeBooks.length,
    afterCount: afterBooks.length
  };
}

// 执行导入
console.log('🎯 执行 batchImportBooks() 开始导入');
batchImportBooks().then(result => {
  console.log('🎉 批量导入完成!', result);
}).catch(error => {
  console.error('💥 批量导入失败:', error);
});
`
}

// 主执行逻辑
try {
  console.log(`📁 扫描目录: ${booksDir}`)
  
  if (!fs.existsSync(booksDir)) {
    console.error(`❌ 目录不存在: ${booksDir}`)
    process.exit(1)
  }
  
  const files = scanDirectory(booksDir)
  
  console.log(`\n📊 扫描结果:`)
  console.log(`  找到 ${files.length} 个电子书文件`)
  
  // 按格式分组统计
  const formatStats = {}
  files.forEach(file => {
    formatStats[file.format] = (formatStats[file.format] || 0) + 1
  })
  
  console.log(`  格式分布:`)
  Object.entries(formatStats).forEach(([format, count]) => {
    console.log(`    ${format.toUpperCase()}: ${count} 个`)
  })
  
  if (files.length === 0) {
    console.log(`\n⚠️  没有找到支持的电子书文件`)
    console.log(`支持的格式: ${supportedFormats.join(', ')}`)
    process.exit(0)
  }
  
  // 显示文件列表
  console.log(`\n📋 文件列表:`)
  files.forEach((file, index) => {
    console.log(`  ${index + 1}. ${file.name} (${file.format.toUpperCase()}, ${(file.size / 1024).toFixed(1)} KB)`)
    console.log(`     路径: ${file.path}`)
    const coverPath = findCoverFile(file.path)
    if (coverPath) {
      console.log(`     封面: ${coverPath}`)
    }
  })
  
  // 生成导入脚本
  const script = generateImportScript(files)
  const scriptPath = path.join(__dirname, 'import-script.js')
  fs.writeFileSync(scriptPath, script, 'utf8')
  
  console.log(`\n✅ 导入脚本已生成: ${scriptPath}`)
  console.log(`\n🎯 下一步操作:`)
  console.log(`1. 在浏览器中打开应用 (http://localhost:5173)`)
  console.log(`2. 打开开发者工具 (F12)`)
  console.log(`3. 在控制台中执行生成的脚本内容`)
  console.log(`4. 观察导入过程和结果`)
  
} catch (error) {
  console.error('💥 脚本执行失败:', error)
  process.exit(1)
}
