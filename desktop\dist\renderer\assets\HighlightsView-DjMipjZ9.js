import{d as o,f as a,c as n,g as e,a as i,r as c,o as l,_ as p}from"./index-BqsStGhU.js";const _={class:"highlights-view"},r={class:"highlights-content"},d=o({__name:"HighlightsView",setup(g){return a(()=>{console.log("高亮管理页面已加载")}),(h,s)=>{const t=c("el-empty");return l(),n("div",_,[s[0]||(s[0]=e("div",{class:"page-header"},[e("h1",{class:"page-title"},"高亮管理"),e("p",{class:"page-description"},"管理文本高亮标记")],-1)),e("div",r,[i(t,{description:"高亮管理功能开发中..."})])])}}}),f=p(d,[["__scopeId","data-v-027369f1"]]);export{f as default};
