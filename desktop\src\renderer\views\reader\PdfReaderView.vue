<!--
  PDF阅读器视图组件
  使用@tato30/vue-pdf进行PDF文档的渲染和交互
-->

<template>
  <div class="pdf-reader-view">
    <!-- 工具栏 -->
    <div class="toolbar">
      <!-- 返回按钮 -->
      <div class="toolbar-left">
        <!-- <el-button @click="goBack" type="primary" :icon="ArrowLeft" circle />
        返回图书列表 -->
          <el-button @click="goBack" type="primary" :icon="ArrowLeft">
          返回图书列表
        </el-button>
      </div>
      

      <div class="book-info" v-if="currentBook">
          <span class="book-title">{{ currentBook.title }}</span>
          <span class="reading-progress">{{ progressText }}</span>
        </div>

      <!-- 页面控制 -->
      <!-- <div class="toolbar-center">
        <div class="page-controls">
          <el-button 
            @click="previousPage" 
            :disabled="currentPage <= 1"
            :icon="ArrowLeft"
            size="small"
          />
          

          
          <el-button 
            @click="nextPage" 
            :disabled="currentPage >= totalPages"
            :icon="ArrowRight"
            size="small"
          />
        </div>
      </div> -->

      <!-- 工具按钮 -->
      <!-- <div class="toolbar-right"> -->
        <!-- 缩放控制 -->
        <!-- <div class="zoom-controls">
          <el-button @click="zoomOut" :icon="ZoomOut" size="small" title="缩小" />
          <el-button @click="zoomIn" :icon="ZoomIn" size="small" title="放大" />
        </div>
      </div> -->
    </div>



    <!-- 主内容区域 -->
    <div class="content-area">
      <!-- 侧边栏 -->
      <div v-if="showSidebar" class="sidebar">
        <!-- 缩略图面板 -->
        <div v-if="sidebarMode === 'thumbnails'" class="thumbnails-panel">
          <h3>缩略图</h3>
          <div class="thumbnail-list">
            <div 
              v-for="pageNum in totalPages" 
              :key="pageNum"
              class="thumbnail-item"
              :class="{ active: pageNum === currentPage }"
              @click="goToPageNumber(pageNum)"
            >
              <div class="thumbnail-page">{{ pageNum }}</div>
            </div>
          </div>
        </div>

        <!-- 书签面板 -->
        <div v-else class="bookmarks-panel">
          <h3>书签</h3>
          <div class="bookmark-list">
            <div 
              v-for="bookmark in bookmarks" 
              :key="bookmark.id"
              class="bookmark-item"
              @click="goToBookmark(bookmark)"
            >
              <div class="bookmark-title">{{ bookmark.title }}</div>
              <div class="bookmark-page">第 {{ bookmark.pageNumber }} 页</div>
            </div>
          </div>
        </div>
      </div>

      <!-- PDF显示区域 -->
      <div class="pdf-viewer" ref="viewerContainer">
        <!-- 加载状态 -->
        <div v-if="isLoading" class="loading-container">
          <el-icon class="is-loading">
            <Loading />
          </el-icon>
          <span>正在加载PDF文档...</span>
        </div>

        <!-- 错误状态 -->
        <div v-else-if="error" class="error-container">
          <el-icon>
            <WarningFilled />
          </el-icon>
          <span>{{ error }}</span>
          <el-button @click="retryLoad" type="primary" size="small">重试</el-button>
        </div>

        <!-- PDF组件 -->
        <div v-else-if="pdfDocument" class="pdf-container">
          <iframe
            :src="pdfDocument"
            class="pdf-iframe"
            @load="onPdfLoaded"
            @error="onPdfLoadingFailed"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowLeft,
  ArrowRight,
  ZoomIn,
  ZoomOut,
  Loading,
  WarningFilled
} from '@element-plus/icons-vue'
import { useReaderStore } from '../../store/reader'

// 导入URL polyfill（必须在@tato30/vue-pdf之前）
import '@renderer/utils/url-polyfill'

// PDF阅读器使用iframe实现



// Props
interface Props {
  /** 书籍ID */
  bookId: string
}

const props = defineProps<Props>()

// Emits
interface Emits {
  (e: 'go-back'): void
}

const emit = defineEmits<Emits>()

// 路由
const router = useRouter()

// 响应式数据
const isLoading = ref(true)
const error = ref<string>('')
const currentPage = ref(1)
const totalPages = ref(0)
const scale = ref(1.0)

const showSidebar = ref(false)
const sidebarMode = ref<'bookmarks' | 'thumbnails'>('bookmarks')
const bookmarks = ref<any[]>([])

// PDF相关
const pdfFilePath = ref('')
const pdfDocument = ref()
const currentBlobUrl = ref('') // 用于跟踪当前的Blob URL以便清理



// PDF文件转换为Blob URL函数
const convertToFileUrl = async (filePath: string): Promise<string> => {
  console.log('原始文件路径:', filePath)

  if (filePath.startsWith('http') || filePath.startsWith('blob:')) {
    return filePath
  }

  try {
    // 通过IPC读取文件内容
    console.log('通过IPC读取PDF文件:', filePath)
    const fileBuffer = await window.electronAPI.file.read(filePath)

    // 将Buffer转换为Blob
    const blob = new Blob([fileBuffer], { type: 'application/pdf' })

    // 创建Blob URL
    const blobUrl = URL.createObjectURL(blob)

    console.log('转换后的Blob URL:', blobUrl)
    return blobUrl
  } catch (error) {
    console.error('读取PDF文件失败:', error)
    throw new Error(`无法读取PDF文件: ${error.message}`)
  }
}

// PDF加载状态监听
watch(pdfFilePath, async (newPath) => {
  console.log('pdfFilePath changed:', newPath)

  // 清理之前的Blob URL
  if (currentBlobUrl.value) {
    console.log('清理之前的Blob URL:', currentBlobUrl.value)
    URL.revokeObjectURL(currentBlobUrl.value)
    currentBlobUrl.value = ''
  }

  if (newPath) {
    try {
      isLoading.value = true
      error.value = ''
      console.log('PDF文档开始加载:', newPath)

      // 异步转换文件路径为Blob URL
      const pdfUrl = await convertToFileUrl(newPath)
      pdfDocument.value = pdfUrl
      currentBlobUrl.value = pdfUrl // 保存当前Blob URL用于后续清理

      // 设置一个超时，如果iframe没有触发load事件
      setTimeout(() => {
        if (isLoading.value) {
          console.log('PDF加载超时，但继续显示')
          isLoading.value = false
        }
      }, 5000) // 增加超时时间，因为文件读取可能需要更长时间

    } catch (err) {
      console.error('PDF加载错误:', err)
      error.value = `PDF加载失败: ${err.message || err}`
      isLoading.value = false
    }
  }
})



// 方法
const goBack = () => {
  emit('go-back')
}

const previousPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--
    updateProgress()
  }
}

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++
    updateProgress()
  }
}



const goToPageNumber = (pageNum: number) => {
  currentPage.value = pageNum
  updateProgress()
}

// 缩放功能
const zoomIn = () => {
  scale.value = Math.min(scale.value * 1.25, 5.0)
}

const zoomOut = () => {
  scale.value = Math.max(scale.value / 1.25, 0.25)
}



const toggleSidebar = () => {
  showSidebar.value = !showSidebar.value
}



// 注意：由于使用iframe方案，大部分高级功能已移除
// 用户可以通过浏览器的PDF查看器进行操作



const onPdfLoaded = (event: any) => {
  console.log('PDF iframe加载完成:', event)
  isLoading.value = false

  // 由于使用iframe，我们无法直接获取页数，设置一个默认值
  if (totalPages.value === 0) {
    totalPages.value = 1 // 默认至少有1页
  }

  // 恢复阅读进度
  restoreReadingProgress()
}

const onPdfLoadingFailed = (error: any) => {
  console.error('PDF iframe加载失败:', error)
  error.value = `PDF加载失败: 无法显示PDF文件`
  isLoading.value = false
}

const updateProgress = async () => {
  if (window.electronAPI?.reader?.updateProgress) {
    const progress = (currentPage.value / totalPages.value) * 100
    await window.electronAPI.reader.updateProgress(props.bookId, progress, currentPage.value)
  }
}

const retryLoad = () => {
  error.value = ''
  loadPdfDocument()
}

const restoreReadingProgress = async () => {
  try {
    // 获取阅读进度
    const progress = await window.electronAPI.reader.getProgress(props.bookId)
    if (progress && progress.currentPage) {
      currentPage.value = progress.currentPage
    }
  } catch (error) {
    console.error('恢复阅读进度失败:', error)
  }
}

const loadPdfDocument = async () => {
  try {
    console.log('开始加载PDF文档，书籍ID:', props.bookId)
    isLoading.value = true
    error.value = ''

    // 获取书籍信息
    const book = await window.electronAPI.reader.getBook(props.bookId)
    console.log('获取到书籍信息:', book)

    if (!book) {
      throw new Error('书籍不存在')
    }

    // 设置PDF文件路径
    const filePath = book.file_path || book.filePath
    console.log('设置PDF文件路径:', filePath)

    if (!filePath) {
      throw new Error('PDF文件路径为空')
    }

    pdfFilePath.value = filePath

    // 加载书签
    await loadBookmarks()

    console.log('PDF文档路径设置完成:', pdfFilePath.value)

  } catch (err) {
    console.error('加载PDF失败:', err)
    error.value = `加载失败: ${err.message || err}`
    isLoading.value = false
  }
}

const loadBookmarks = async () => {
  // 书签功能已删除，不再加载书签
  console.log('书签功能已禁用')
}

// 键盘快捷键处理
const handleKeydown = (event: KeyboardEvent) => {
  // 防止在输入框中触发快捷键
  if (event.target instanceof HTMLInputElement) {
    return
  }

  switch (event.key) {
    case 'ArrowLeft':
    case 'PageUp':
      event.preventDefault()
      previousPage()
      break
    case 'ArrowRight':
    case 'PageDown':
    case ' ': // 空格键
      event.preventDefault()
      nextPage()
      break
    case '+':
    case '=':
      event.preventDefault()
      zoomIn()
      break
    case '-':
      event.preventDefault()
      zoomOut()
      break
    case '0':
      event.preventDefault()
      resetZoom()
      break
    case 'f':
    case 'F':
      if (event.ctrlKey || event.metaKey) {
        event.preventDefault()
        toggleSearch()
      }
      break
    case 'Escape':
      if (showSearch.value) {
        event.preventDefault()
        toggleSearch()
      }
      break
  }
}

// Store
const readerStore = useReaderStore()

// 计算属性
const {
  currentBook
} = readerStore

// 生命周期
onMounted(() => {
  loadPdfDocument()
  // 添加键盘事件监听
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  // 清理资源
  document.removeEventListener('keydown', handleKeydown)

  // 清理Blob URL
  if (currentBlobUrl.value) {
    console.log('组件卸载时清理Blob URL:', currentBlobUrl.value)
    URL.revokeObjectURL(currentBlobUrl.value)
    currentBlobUrl.value = ''
  }
})
</script>

<style scoped>
.pdf-reader-view {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f5f5f5;
}

.toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px;
  background: white;
  border-bottom: 1px solid #e0e0e0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.toolbar-left,
.toolbar-center,
.toolbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}


.zoom-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}


.content-area {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.sidebar {
  width: 300px;
  background: white;
  border-right: 1px solid #e0e0e0;
  overflow-y: auto;
}

.thumbnails-panel,
.bookmarks-panel {
  padding: 16px;
}

.thumbnails-panel h3,
.bookmarks-panel h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.thumbnail-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  gap: 8px;
}

.thumbnail-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px;
  border: 2px solid transparent;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.thumbnail-item:hover {
  background: #f0f0f0;
}

.thumbnail-item.active {
  border-color: #409eff;
  background: #e6f7ff;
}

.thumbnail-page {
  width: 60px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 12px;
  color: #666;
}

.bookmark-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.bookmark-item {
  padding: 12px;
  background: #f9f9f9;
  border-radius: 4px;
  cursor: pointer;
  transition: background 0.2s;
}

.bookmark-item:hover {
  background: #e6f7ff;
}

.bookmark-title {
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.bookmark-page {
  font-size: 12px;
  color: #666;
}

.pdf-viewer {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: auto;
  background: #e5e5e5;
}

.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 40px;
  color: #666;
}

.loading-container .el-icon {
  font-size: 32px;
}

.error-container .el-icon {
  font-size: 32px;
  color: #f56c6c;
}

.pdf-container {
  display: flex;
  justify-content: center;
  padding: 10px;
  width: 100%;
  height: 100%;
}

.pdf-iframe {
  width: 100%;
  height: 100%;
  border: none;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  background: white;
  transition: transform 0.2s ease;
  max-width: 100%;
  max-height: 100%;
}

.pdf-iframe:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}

/* PDF文本层样式 */
:deep(.textLayer) {
  opacity: 0.2;
}

:deep(.textLayer:hover) {
  opacity: 1;
}

/* PDF高亮样式 */
:deep(.highlight) {
  background-color: rgba(255, 255, 0, 0.3);
  border-radius: 2px;
}

/* PDF注释层样式 */
:deep(.annotationLayer) {
  opacity: 0.9;
}

/* 滚动条样式 */
.pdf-viewer::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.pdf-viewer::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.pdf-viewer::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.pdf-viewer::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .toolbar {
    flex-wrap: wrap;
    gap: 8px;
  }

  .toolbar-center {
    order: 3;
    width: 100%;
    justify-content: center;
  }

  .sidebar {
    width: 250px;
  }

  .thumbnail-list {
    grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
  }
}
</style>
